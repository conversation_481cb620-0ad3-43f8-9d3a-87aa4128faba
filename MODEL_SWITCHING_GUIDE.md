# Model Switching Guide

This guide explains how to use either ChatGPT or Gemini classifier exclusively in the SKU Categorisation Pipeline.

## Quick Start

### 1. Configure API Keys

Copy the example environment file and add your API keys:

```bash
cp .env.example .env
```

Edit `.env` and add your API keys:
- For OpenAI: Get your key from https://platform.openai.com/api-keys
- For Gemini: Get your key from https://makersuite.google.com/app/apikey

### 2. Choose Your Model Provider

Set the `MODEL_PROVIDER` in your `.env` file:

```bash
# For ChatGPT
MODEL_PROVIDER=openai

# For Gemini
MODEL_PROVIDER=gemini
```

## Using the Model Switcher

The `model_switcher.py` utility makes it easy to switch between providers:

### Check Current Status
```bash
python model_switcher.py --status
```

### Switch to ChatGPT
```bash
python model_switcher.py --switch openai
```

### Switch to Gemini
```bash
python model_switcher.py --switch gemini
```

### Setup Environment File
```bash
python model_switcher.py --setup --openai-key YOUR_KEY --gemini-key YOUR_KEY
```

## Running the Pipeline

### Using Default Provider (from .env)
```bash
python main.py --input data/input.xlsx --output data/output.xlsx
```

### Override Provider via Command Line
```bash
# Use ChatGPT
python main.py --model openai --input data/input.xlsx

# Use Gemini  
python main.py --model gemini --input data/input.xlsx
```

## Important Notes

- **Only one model is used at a time** - the pipeline will use either ChatGPT OR Gemini, never both simultaneously
- **API Key Required** - You only need the API key for the provider you want to use
- **Switching is Easy** - You can switch between providers anytime using the model switcher utility
- **Session vs Permanent** - Command line switches are temporary; update `.env` for permanent changes

## Configuration Priority

The system uses this priority order for determining which model to use:

1. Command line argument (`--model`)
2. Environment variable (`MODEL_PROVIDER`)
3. Default value (`openai`)

## Troubleshooting

### "API key not configured" Error
- Make sure you've set the correct API key in your `.env` file
- Check that the API key is valid and has sufficient credits/quota

### "Provider not available" Error
- Verify the provider name is either `openai` or `gemini`
- Ensure the corresponding API key is configured

### Check Available Providers
```bash
python model_switcher.py --status
```

This will show which providers are properly configured and available.
