"""
Unit Tests for SKU Categorisation Pipeline
Tests individual components and integration
"""
import unittest
import pandas as pd
import tempfile
import os
from pathlib import Path
import json

# Import pipeline components
from config import Config
from input_processor import InputProcessor
from batch_processor import BatchProcessor
from gpt_classifier import GPTClassifier
from gemini_classifier import GeminiClassifier
from unified_classifier import UnifiedClassifier, ClassifierFactory
from null_detector import NullDetector
from output_processor import OutputProcessor
from cache_manager import <PERSON>ache<PERSON>anager
from generate_sample_data import generate_sample_sku_data

class TestInputProcessor(unittest.TestCase):
    """Test input processing functionality"""
    
    def setUp(self):
        self.config = Config()
        self.processor = InputProcessor(self.config)
        self.sample_df = generate_sample_sku_data(10)
    
    def test_load_sku_data_from_dataframe(self):
        """Test loading data from DataFrame"""
        # Create temporary CSV file
        with tempfile.NamedTemporaryFile(suffix='.csv', delete=False) as tmp:
            tmp_path = tmp.name

        # Write data to file (outside the context manager)
        self.sample_df.to_csv(tmp_path, index=False)

        try:
            # Test loading
            loaded_df = self.processor.load_sku_data(tmp_path)
            self.assertEqual(len(loaded_df), len(self.sample_df))
        finally:
            # Cleanup
            try:
                os.unlink(tmp_path)
            except (OSError, PermissionError):
                pass  # Ignore cleanup errors on Windows
    
    def test_clean_and_normalize(self):
        """Test data cleaning and normalization"""
        # Create test data with messy text
        messy_df = self.sample_df.copy()
        messy_df.loc[0, 'SKUName'] = '  Extra   Spaces  '
        messy_df.loc[1, 'CompanyName'] = 'Company\t\tWith\nTabs'
        
        cleaned_df = self.processor.clean_and_normalize(messy_df)
        
        # Check cleaning
        self.assertEqual(cleaned_df.loc[0, 'SKUName'], 'Extra Spaces')
        self.assertEqual(cleaned_df.loc[1, 'CompanyName'], 'Company With Tabs')
    
    def test_prepare_for_processing(self):
        """Test preparation for processing"""
        processed_df, metadata = self.processor.prepare_for_processing(self.sample_df)
        
        self.assertIn('original_index', processed_df.columns)
        self.assertEqual(metadata['processed_rows'], len(processed_df))
        self.assertIsInstance(metadata, dict)

class TestBatchProcessor(unittest.TestCase):
    """Test batch processing functionality"""
    
    def setUp(self):
        self.config = Config()
        self.config.BATCH_SIZE = 5
        self.processor = BatchProcessor(self.config)
        self.sample_df = generate_sample_sku_data(12)
    
    def test_create_batches(self):
        """Test batch creation"""
        batches = self.processor.create_batches(self.sample_df)
        
        # Should create 3 batches (5, 5, 2)
        self.assertEqual(len(batches), 3)
        self.assertEqual(len(batches[0]), 5)
        self.assertEqual(len(batches[1]), 5)
        self.assertEqual(len(batches[2]), 2)
    
    def test_batch_generator(self):
        """Test batch generator"""
        batches = list(self.processor.batch_generator(self.sample_df))
        
        self.assertEqual(len(batches), 3)
        # Check batch numbers
        self.assertEqual(batches[0][0], 1)  # First batch number
        self.assertEqual(batches[1][0], 2)  # Second batch number
        self.assertEqual(batches[2][0], 3)  # Third batch number
    
    def test_format_batch_for_prompt(self):
        """Test batch formatting for prompts"""
        batch_df = self.sample_df.head(3)
        batch_data = self.processor.format_batch_for_prompt(batch_df)
        
        self.assertEqual(len(batch_data), 3)
        self.assertIn('sku_name', batch_data[0])
        self.assertIn('company_name', batch_data[0])

class TestNullDetector(unittest.TestCase):
    """Test null classification detection"""
    
    def setUp(self):
        self.detector = NullDetector()
    
    def test_is_null_classification(self):
        """Test null classification detection"""
        # Test null classification
        null_classification = {'Tier0': None, 'Tier1': None, 'Tier2': None}
        self.assertTrue(self.detector.is_null_classification(null_classification))
        
        # Test valid classification
        valid_classification = {'Tier0': 'Food', 'Tier1': 'Snacks', 'Tier2': 'Chips'}
        self.assertFalse(self.detector.is_null_classification(valid_classification))
        
        # Test partial classification
        partial_classification = {'Tier0': 'Food', 'Tier1': None, 'Tier2': None}
        self.assertFalse(self.detector.is_null_classification(partial_classification))
    
    def test_is_partial_classification(self):
        """Test partial classification detection"""
        # Test partial classification
        partial_classification = {'Tier0': 'Food', 'Tier1': None, 'Tier2': None}
        self.assertTrue(self.detector.is_partial_classification(partial_classification))
        
        # Test complete classification
        complete_classification = {'Tier0': 'Food', 'Tier1': 'Snacks', 'Tier2': 'Chips'}
        self.assertFalse(self.detector.is_partial_classification(complete_classification))
        
        # Test null classification
        null_classification = {'Tier0': None, 'Tier1': None, 'Tier2': None}
        self.assertFalse(self.detector.is_partial_classification(null_classification))

class TestOutputProcessor(unittest.TestCase):
    """Test output processing functionality"""
    
    def setUp(self):
        self.processor = OutputProcessor()
        self.sample_df = generate_sample_sku_data(5)
        
        # Add original_index column
        self.sample_df['original_index'] = self.sample_df.index
    
    def test_consolidate_results(self):
        """Test result consolidation"""
        # Create mock batch results
        batch_results = [{
            'success': True,
            'classifications': [
                {'Tier0': 'Food', 'Tier1': 'Snacks', 'Tier2': 'Chips'},
                {'Tier0': 'Personal Care', 'Tier1': 'Hair Care', 'Tier2': 'Shampoo'}
            ],
            'batch_metadata': {
                'original_indices': [0, 1]
            }
        }]
        
        result_df = self.processor.consolidate_results(self.sample_df, batch_results)
        
        # Check that output columns were added
        for col in self.processor.config.OUTPUT_COLUMNS:
            self.assertIn(col, result_df.columns)
        
        # Check that classifications were applied
        self.assertEqual(result_df.loc[0, 'Tier0_Segment'], 'Food')
        self.assertEqual(result_df.loc[1, 'Tier0_Segment'], 'Personal Care')
    
    def test_generate_summary_stats(self):
        """Test summary statistics generation"""
        # Create test DataFrame with classifications
        test_df = self.sample_df.copy()
        test_df['Tier0_Segment'] = ['Food', 'Personal Care', None, 'Food', 'Home Care']
        test_df['Tier1_Category'] = ['Snacks', 'Hair Care', None, 'Beverages', 'Cleaning']
        test_df['Tier2_SubCategory'] = ['Chips', 'Shampoo', None, 'Tea', 'Detergent']
        test_df['Source'] = ['GPT Primary', 'GPT Primary', 'Unclassified', 'Web Fallback', 'GPT Primary']
        test_df['NeedsManualReview'] = [False, False, True, False, False]
        
        stats = self.processor.generate_summary_stats(test_df)
        
        self.assertEqual(stats['total_skus'], 5)
        self.assertEqual(stats['complete_classifications'], 4)
        self.assertEqual(stats['needs_manual_review'], 1)

class TestCacheManager(unittest.TestCase):
    """Test cache management functionality"""
    
    def setUp(self):
        # Create temporary cache file
        self.temp_cache = tempfile.NamedTemporaryFile(suffix='.json', delete=False)
        self.temp_cache.close()
        
        # Create config with temporary cache file
        self.config = Config()
        self.config.CACHE_FILE = self.temp_cache.name
        self.config.ENABLE_CACHING = True
        
        self.cache_manager = CacheManager(self.config)
    
    def tearDown(self):
        # Cleanup temporary file
        if os.path.exists(self.temp_cache.name):
            os.unlink(self.temp_cache.name)
    
    def test_cache_and_retrieve_classification(self):
        """Test caching and retrieving classifications"""
        # Cache a classification
        classification = {'Tier0': 'Food', 'Tier1': 'Snacks', 'Tier2': 'Chips'}
        self.cache_manager.cache_classification('Test SKU', 'Test Company', classification, 'test')
        
        # Retrieve cached classification
        cached = self.cache_manager.get_cached_classification('Test SKU', 'Test Company')
        
        self.assertIsNotNone(cached)
        self.assertEqual(cached['classification'], classification)
        self.assertEqual(cached['source'], 'test')
    
    def test_cache_key_generation(self):
        """Test cache key generation consistency"""
        key1 = self.cache_manager._generate_cache_key('Test SKU', 'Test Company')
        key2 = self.cache_manager._generate_cache_key('test sku', 'test company')
        key3 = self.cache_manager._generate_cache_key('Test SKU ', ' Test Company')
        
        # Keys should be the same due to normalization
        self.assertEqual(key1, key2)
        self.assertEqual(key1, key3)

class TestUnifiedClassifier(unittest.TestCase):
    """Test unified classifier functionality"""

    def setUp(self):
        # Create test configuration for OpenAI (default)
        self.config = Config()
        self.config.MODEL_PROVIDER = 'openai'
        self.config.OPENAI_API_KEY = 'test_key'  # Mock key for testing

    def test_classifier_initialization(self):
        """Test classifier initialization with different providers"""
        # Test OpenAI initialization
        openai_classifier = UnifiedClassifier(self.config)
        model_info = openai_classifier.get_model_info()
        self.assertEqual(model_info['provider'], 'openai')

        # Test Gemini initialization (if API key is available)
        gemini_config = Config()
        gemini_config.MODEL_PROVIDER = 'gemini'
        gemini_config.GEMINI_API_KEY = 'test_key'  # Mock key for testing

        try:
            gemini_classifier = UnifiedClassifier(gemini_config)
            model_info = gemini_classifier.get_model_info()
            self.assertEqual(model_info['provider'], 'gemini')
        except Exception:
            # Skip if Gemini dependencies not available
            pass

    def test_classifier_factory(self):
        """Test classifier factory functionality"""
        # Test available providers
        providers = ClassifierFactory.get_available_providers()
        self.assertIn('openai', providers)
        self.assertIn('gemini', providers)

        # Test provider validation
        validation = ClassifierFactory.validate_provider_config('openai', self.config)
        self.assertTrue(validation['valid'])

        # Test invalid provider
        validation = ClassifierFactory.validate_provider_config('invalid', self.config)
        self.assertFalse(validation['valid'])
        self.assertIn('Unknown provider', validation['errors'][0])

class TestGeminiClassifier(unittest.TestCase):
    """Test Gemini classifier functionality"""

    def setUp(self):
        self.config = Config()
        self.config.GEMINI_API_KEY = 'test_key'  # Mock key for testing

    def test_response_cleaning(self):
        """Test Gemini response cleaning"""
        try:
            classifier = GeminiClassifier(self.config)

            # Test markdown removal
            response_with_markdown = '```json\n[{"Tier0": "Food"}]\n```'
            cleaned = classifier.clean_gemini_response(response_with_markdown)
            self.assertEqual(cleaned, '[{"Tier0": "Food"}]')

            # Test plain JSON
            plain_json = '[{"Tier0": "Food"}]'
            cleaned = classifier.clean_gemini_response(plain_json)
            self.assertEqual(cleaned, '[{"Tier0": "Food"}]')

        except Exception:
            # Skip if Gemini dependencies not available
            self.skipTest("Gemini dependencies not available")

    def test_null_classification_detection(self):
        """Test null classification detection for Gemini"""
        try:
            classifier = GeminiClassifier(self.config)

            # Test null classification
            null_classification = {'Tier0': None, 'Tier1': None, 'Tier2': None}
            self.assertTrue(classifier.is_null_classification(null_classification))

            # Test valid classification
            valid_classification = {'Tier0': 'Food', 'Tier1': 'Snacks', 'Tier2': 'Chips'}
            self.assertFalse(classifier.is_null_classification(valid_classification))

        except Exception:
            # Skip if Gemini dependencies not available
            self.skipTest("Gemini dependencies not available")

class TestIntegration(unittest.TestCase):
    """Integration tests for the complete pipeline"""

    def setUp(self):
        # Create test configuration
        self.config = Config()
        self.config.BATCH_SIZE = 3
        self.config.ENABLE_WEB_FALLBACK = False  # Disable for testing
        self.config.ENABLE_CACHING = False  # Disable for testing

        # Create sample data
        self.sample_df = generate_sample_sku_data(5)

    def test_end_to_end_processing(self):
        """Test end-to-end processing without external API calls"""
        # This test would require mocking the API calls
        # For now, we'll test the data flow structure

        input_processor = InputProcessor(self.config)
        batch_processor = BatchProcessor(self.config)

        # Process input
        processed_df, _ = input_processor.prepare_for_processing(self.sample_df)

        # Create batches
        batches = batch_processor.create_batches(processed_df)

        # Verify batch structure
        self.assertGreater(len(batches), 0)
        self.assertEqual(sum(len(batch) for batch in batches), len(processed_df))

    def test_multi_model_configuration(self):
        """Test configuration with different models"""
        # Test OpenAI configuration
        openai_config = Config()
        openai_config.MODEL_PROVIDER = 'openai'
        openai_config.OPENAI_API_KEY = 'test_key'

        try:
            classifier = UnifiedClassifier(openai_config)
            self.assertEqual(classifier.get_model_info()['provider'], 'openai')
        except Exception:
            pass  # Skip if dependencies not available

        # Test Gemini configuration
        gemini_config = Config()
        gemini_config.MODEL_PROVIDER = 'gemini'
        gemini_config.GEMINI_API_KEY = 'test_key'

        try:
            classifier = UnifiedClassifier(gemini_config)
            self.assertEqual(classifier.get_model_info()['provider'], 'gemini')
        except Exception:
            pass  # Skip if dependencies not available

def run_tests():
    """Run all tests"""
    # Create test suite
    test_suite = unittest.TestSuite()
    
    # Add test classes
    test_classes = [
        TestInputProcessor,
        TestBatchProcessor,
        TestNullDetector,
        TestOutputProcessor,
        TestCacheManager,
        TestUnifiedClassifier,
        TestGeminiClassifier,
        TestIntegration
    ]
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    return result.wasSuccessful()

if __name__ == "__main__":
    success = run_tests()
    if success:
        print("\nAll tests passed!")
    else:
        print("\nSome tests failed!")
        exit(1)
