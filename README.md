# SKU Categorisation Pipeline

A comprehensive pipeline for automatically categorizing SKU (Stock Keeping Unit) data using GPT-4o-mini with web search fallback for uncertain classifications.

## Features

- **Batch Processing**: Efficiently processes large SKU datasets in configurable batches
- **Multi-Model Support**: Uses OpenAI GPT-4o-mini or Google Gemini for intelligent classification
- **Web Search Fallback**: Automatically searches for product information when GPT is uncertain
- **Three-Tier Classification**: Categorizes products into Tier0 (Segment), Tier1 (Category), and Tier2 (Subcategory)
- **Caching System**: Avoids reprocessing previously classified SKUs
- **Comprehensive Logging**: Detailed logs and audit trails
- **CSV Output**: Generates enriched CSV files with separate files for different data views

## Installation

1. **Clone or download the project files**

2. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

3. **Get API Keys**:
   - **OpenAI**: Get your API key from [OpenAI Platform](https://platform.openai.com/api-keys)
   - **Gemini**: Get your API key from [Google AI Studio](https://makersuite.google.com/app/apikey)

4. **Set up environment variables**:
   ```bash
   cp .env.example .env
   # Edit .env and add your API keys (OpenAI and/or Gemini)
   ```

5. **Create sample data** (optional):
   ```bash
   python generate_sample_data.py
   ```

## Configuration

Edit `config.py` or use environment variables:

- `MODEL_PROVIDER`: Choose 'openai' or 'gemini' (default: 'openai')
- `OPENAI_API_KEY`: Your OpenAI API key (required for OpenAI models)
- `GEMINI_API_KEY`: Your Google Gemini API key (required for Gemini models)
- `BATCH_SIZE`: Number of SKUs to process per batch (default: 20)
- `ENABLE_WEB_FALLBACK`: Enable web search for uncertain classifications (default: True)
- `ENABLE_CACHING`: Enable caching of classifications (default: True)

## Usage

### Basic Usage

**Using OpenAI GPT (default)**:
```bash
python main.py --input data/SKU_Categorisation_Master.csv --output data/SKU_Categorisation_Enriched.csv
```

**Using Google Gemini**:
```bash
python main.py --model gemini --input data/SKU_Categorisation_Master.csv --output data/SKU_Categorisation_Enriched.csv
```

### Command Line Options

```bash
python main.py --help
```

Options:

- `--input, -i`: Input file path (Excel or CSV)
- `--output, -o`: Output file path
- `--batch-size`: Override default batch size
- `--model`: Choose model provider ('openai' or 'gemini')
- `--no-fallback`: Disable web search fallback
- `--no-cache`: Disable caching

### Input File Format

Your input file should contain these columns:
- `SKUId`: Unique SKU identifier
- `SKUName`: Product name
- `PrimaryCategoryName`: Existing primary category
- `SecondaryCategoryName`: Existing secondary category
- `CompanyId`: Company identifier
- `CompanyName`: Company name

### Output Format

The pipeline generates multiple CSV files:

1. **Main Results File** (e.g., `SKU_Categorisation_Enriched.csv`): Contains original data plus:
   - `Tier0_Segment`: Broad product segment
   - `Tier1_Category`: Product category
   - `Tier2_SubCategory`: Detailed subcategory
   - `Source`: Classification source (GPT Primary/Web Fallback)
   - `NeedsManualReview`: Flag for items requiring manual review

2. **Summary Statistics File** (e.g., `SKU_Categorisation_Enriched_Summary.csv`): Processing statistics and quality metrics

3. **Manual Review File** (e.g., `SKU_Categorisation_Enriched_ManualReview.csv`): Items that need manual classification (only created if there are items requiring review)

## Pipeline Steps

1. **Input Preparation**: Load and clean SKU data
2. **Batch Creation**: Split data into manageable batches
3. **GPT Classification**: Process batches using GPT-4o-mini
4. **Null Detection**: Identify uncertain classifications
5. **Web Search Fallback**: Enhance uncertain items with web search
6. **Result Consolidation**: Combine all classifications
7. **Output Generation**: Create enriched CSV files

## Testing

Run the test suite:

```bash
python test_pipeline.py
```

Generate sample data for testing:

```bash
python generate_sample_data.py
```

## Architecture

### Core Components

- `input_processor.py`: Data loading and preparation
- `batch_processor.py`: Batch creation and management
- `gpt_classifier.py`: GPT API integration and classification
- `null_detector.py`: Uncertain classification detection
- `web_fallback.py`: Web search fallback processing
- `output_processor.py`: Result consolidation and file generation
- `cache_manager.py`: Classification caching system
- `config.py`: Configuration management
- `main.py`: Main pipeline orchestrator

### Data Flow

```
Input File → Input Processor → Batch Processor → GPT Classifier
                                                       ↓
Output File ← Output Processor ← Web Fallback ← Null Detector
```

## Configuration Options

### Model Settings

**OpenAI GPT**:

- Model: gpt-4o-mini (configurable)
- Temperature: 0.1 (low for consistency)
- Max tokens: 2000

**Google Gemini**:

- Model: gemini-1.5-flash (configurable)
- Temperature: 0.1 (low for consistency)
- Max tokens: 2000

### Batch Processing
- Default batch size: 20 SKUs
- Retry logic: 3 attempts with exponential backoff
- Rate limiting: Built-in delays

### Web Search Fallback
- Search sites: BigBasket, Amazon.in, Flipkart
- Max results per search: 3
- Automatic query construction

### Caching
- JSON-based cache storage
- MD5 hash keys for SKU-Company combinations
- Automatic cache validation

## Error Handling

- Comprehensive exception handling at each pipeline step
- Automatic retry for transient failures
- Graceful degradation when services are unavailable
- Detailed error logging and reporting

## Performance Considerations

- Batch processing reduces API calls
- Caching prevents duplicate processing
- Configurable batch sizes for memory management
- Rate limiting to respect API limits

## Troubleshooting

### Common Issues

1. **OpenAI API Key Error**:
   - Ensure your API key is set in the `.env` file
   - Verify the key has sufficient credits

2. **Input File Format Error**:
   - Check that all required columns are present
   - Ensure file is in CSV format (Excel files are also supported for input)

3. **Memory Issues with Large Files**:
   - Reduce batch size using `--batch-size` parameter
   - Process files in smaller chunks

4. **Web Search Failures**:
   - Disable fallback with `--no-fallback` if needed
   - Check internet connectivity

### Logs

Check the following log files:
- `logs/pipeline.log`: Main pipeline execution log
- `logs/processing_log_*.json`: Detailed processing results

## License

This project is provided as-is for educational and commercial use.

## Support

For issues and questions, please check the logs and error messages for detailed information about any problems encountered during processing.
