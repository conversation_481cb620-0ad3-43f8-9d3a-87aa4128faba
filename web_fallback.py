"""
Web Search Fallback System for SKU Categorisation Pipeline
Handles web search-based fallback for uncertain classifications
"""
import requests
import logging
import time
from typing import List, Dict, Optional
from urllib.parse import quote_plus
from bs4 import BeautifulSoup
import pandas as pd
from datetime import datetime, timedelta
from config import Config
from unified_classifier import UnifiedClassifier

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class SearchRateLimiter:
    """Rate limiter for web search requests"""

    def __init__(self, requests_per_minute: int = 20, burst_limit: int = 5, delay_seconds: float = 2.0):
        self.requests_per_minute = requests_per_minute
        self.burst_limit = burst_limit
        self.delay_seconds = delay_seconds
        self.request_times = []
        self.last_request_time = None

    def wait_if_needed(self):
        """Wait if necessary to respect rate limits"""
        current_time = datetime.now()

        # Clean old request times (older than 1 minute)
        cutoff_time = current_time - timedelta(minutes=1)
        self.request_times = [t for t in self.request_times if t > cutoff_time]

        # Check if we need to wait due to per-minute limit
        if len(self.request_times) >= self.requests_per_minute:
            oldest_request = min(self.request_times)
            wait_until = oldest_request + timedelta(minutes=1)
            if current_time < wait_until:
                wait_seconds = (wait_until - current_time).total_seconds()
                logger.info(f"Rate limit reached. Waiting {wait_seconds:.1f} seconds...")
                time.sleep(wait_seconds)
                current_time = datetime.now()

        # Check if we need to wait due to minimum delay between requests
        if self.last_request_time:
            time_since_last = (current_time - self.last_request_time).total_seconds()
            if time_since_last < self.delay_seconds:
                wait_seconds = self.delay_seconds - time_since_last
                logger.debug(f"Enforcing minimum delay. Waiting {wait_seconds:.1f} seconds...")
                time.sleep(wait_seconds)
                current_time = datetime.now()

        # Check burst limit (requests in last 10 seconds)
        burst_cutoff = current_time - timedelta(seconds=10)
        recent_requests = [t for t in self.request_times if t > burst_cutoff]
        if len(recent_requests) >= self.burst_limit:
            wait_seconds = self.delay_seconds * 2  # Double delay for burst protection
            logger.info(f"Burst limit reached. Waiting {wait_seconds:.1f} seconds...")
            time.sleep(wait_seconds)
            current_time = datetime.now()

        # Record this request
        self.request_times.append(current_time)
        self.last_request_time = current_time

    def get_stats(self) -> Dict:
        """Get rate limiter statistics"""
        current_time = datetime.now()
        cutoff_time = current_time - timedelta(minutes=1)
        recent_requests = [t for t in self.request_times if t > cutoff_time]

        return {
            'requests_last_minute': len(recent_requests),
            'requests_per_minute_limit': self.requests_per_minute,
            'delay_seconds': self.delay_seconds,
            'burst_limit': self.burst_limit
        }

class WebFallbackProcessor:
    """Handles web search-based fallback processing for uncertain SKU classifications"""
    
    def __init__(self, config: Config = None):
        self.config = config or Config()
        self.classifier = UnifiedClassifier(config)
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })

        # Initialize rate limiter
        self.rate_limiter = SearchRateLimiter(
            requests_per_minute=getattr(self.config, 'SEARCH_REQUESTS_PER_MINUTE', 20),
            burst_limit=getattr(self.config, 'SEARCH_BURST_LIMIT', 5),
            delay_seconds=getattr(self.config, 'SEARCH_DELAY_SECONDS', 2.0)
        )

        logger.info(f"Web search rate limiter initialized: {self.rate_limiter.requests_per_minute} req/min, "
                   f"{self.rate_limiter.delay_seconds}s delay, burst limit: {self.rate_limiter.burst_limit}")
    
    def construct_search_query(self, sku_name: str, company_name: str) -> str:
        """
        Construct search query for SKU
        
        Args:
            sku_name: Name of the SKU
            company_name: Company name
            
        Returns:
            Formatted search query
        """
        # Clean and format the search terms
        sku_clean = sku_name.strip().replace('"', '')
        company_clean = company_name.strip().replace('"', '')
        
        # Create site-specific search
        sites = ' OR '.join(f'site:{site}' for site in self.config.SEARCH_SITES)
        
        query = f'"{sku_clean}" {company_clean} ({sites})'
        return query
    
    def search_google(self, query: str, max_results: int = None) -> List[Dict]:
        """
        Perform Google search with rate limiting (simplified version - in production use proper search API)

        Args:
            query: Search query
            max_results: Maximum number of results

        Returns:
            List of search result dictionaries
        """
        max_results = max_results or self.config.MAX_SEARCH_RESULTS

        # Apply rate limiting before making the request
        logger.debug(f"Applying rate limiting for search query: {query[:50]}...")
        self.rate_limiter.wait_if_needed()

        # Note: This is a simplified implementation
        # In production, use Google Custom Search API or similar service
        try:
            search_url = f"https://www.google.com/search?q={quote_plus(query)}&num={max_results}"

            logger.debug(f"Making search request to: {search_url[:100]}...")
            response = self.session.get(search_url, timeout=15)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.content, 'html.parser')
            results = []
            
            # Extract search results (simplified parsing)
            for result_div in soup.find_all('div', class_='g')[:max_results]:
                title_elem = result_div.find('h3')
                link_elem = result_div.find('a')
                snippet_elem = result_div.find('span', class_='aCOpRe')
                
                if title_elem and link_elem:
                    result = {
                        'title': title_elem.get_text(strip=True),
                        'url': link_elem.get('href', ''),
                        'snippet': snippet_elem.get_text(strip=True) if snippet_elem else ''
                    }
                    results.append(result)
            
            logger.info(f"Search completed: found {len(results)} results for query: {query[:50]}...")
            return results

        except requests.exceptions.RequestException as e:
            logger.error(f"Network error during search for query '{query[:50]}...': {str(e)}")
            return []
        except Exception as e:
            logger.error(f"Search failed for query '{query[:50]}...': {str(e)}")
            return []
    
    def extract_product_info(self, search_results: List[Dict]) -> str:
        """
        Extract relevant product information from search results
        
        Args:
            search_results: List of search result dictionaries
            
        Returns:
            Formatted product information text
        """
        if not search_results:
            return "No search results found."
        
        info_parts = ["Search results:"]
        
        for i, result in enumerate(search_results, 1):
            title = result.get('title', 'No title')
            snippet = result.get('snippet', 'No description')
            url = result.get('url', '')
            
            info_parts.append(f"{i}. {title}")
            if snippet:
                info_parts.append(f"   Description: {snippet}")
            if url:
                info_parts.append(f"   URL: {url}")
            info_parts.append("")  # Empty line for separation
        
        return "\n".join(info_parts)
    
    def create_fallback_prompt(self, sku_info: Dict, search_info: str) -> str:
        """
        Create enhanced prompt with search results for fallback classification
        
        Args:
            sku_info: SKU information dictionary
            search_info: Formatted search results
            
        Returns:
            Enhanced user prompt
        """
        prompt = f"""SKU to classify:
- Company: {sku_info['company_name']}
- SKU: {sku_info['sku_name']}
- Primary: {sku_info['primary_category']}
- Secondary: {sku_info['secondary_category']}

{search_info}

Based on the SKU details and search results above, classify this product."""
        
        return prompt
    
    def process_fallback_item(self, sku_info: Dict) -> Dict:
        """
        Process a single SKU through web search fallback
        
        Args:
            sku_info: SKU information dictionary
            
        Returns:
            Classification result dictionary
        """
        try:
            # Construct search query
            query = self.construct_search_query(
                sku_info['sku_name'], 
                sku_info['company_name']
            )
            
            # Perform search
            search_results = self.search_google(query)
            
            if not search_results:
                logger.warning(f"No search results for SKU: {sku_info['sku_name']}")
                return {
                    'classification': self._get_null_classification(),
                    'source': 'web_fallback_failed',
                    'search_query': query,
                    'search_results_count': 0,
                    'success': False
                }
            
            # Extract product information
            search_info = self.extract_product_info(search_results)
            
            # Create enhanced prompt
            fallback_prompt = self.create_fallback_prompt(sku_info, search_info)
            
            # Enhanced system prompt for fallback
            enhanced_system_prompt = self.config.SYSTEM_PROMPT + "\n\nHere are relevant search results. Now try classifying based on both the SKU details and the search information."

            # Call classifier with enhanced information
            response = self.classifier.call_api(enhanced_system_prompt, fallback_prompt)

            # Parse single classification (not array)
            classifications = self.classifier.parse_response(f"[{response}]", 1)
            classification = classifications[0] if classifications else self._get_null_classification()
            
            return {
                'classification': classification,
                'source': 'web_fallback',
                'search_query': query,
                'search_results_count': len(search_results),
                'search_results': search_results,
                'raw_response': response,
                'success': True
            }
            
        except Exception as e:
            logger.error(f"Fallback processing failed for SKU {sku_info['sku_name']}: {str(e)}")
            return {
                'classification': self._get_null_classification(),
                'source': 'web_fallback_error',
                'error': str(e),
                'success': False
            }
    
    def process_fallback_batch(self, fallback_df: pd.DataFrame) -> List[Dict]:
        """
        Process a batch of SKUs through web search fallback
        
        Args:
            fallback_df: DataFrame with SKUs needing fallback processing
            
        Returns:
            List of fallback results
        """
        if fallback_df.empty:
            logger.info("No items to process in fallback")
            return []
        
        logger.info(f"Processing {len(fallback_df)} items through web search fallback")

        # Log rate limiter configuration
        rate_stats = self.rate_limiter.get_stats()
        logger.info(f"Rate limiter settings: {rate_stats['requests_per_minute_limit']} req/min, "
                   f"{rate_stats['delay_seconds']}s delay, burst limit: {rate_stats['burst_limit']}")

        fallback_results = []

        for idx, row in fallback_df.iterrows():
            sku_info = {
                'sku_id': row.get('SKUId'),
                'sku_name': row.get('SKUName', ''),
                'company_name': row.get('CompanyName', ''),
                'primary_category': row.get('PrimaryCategoryName', ''),
                'secondary_category': row.get('SecondaryCategoryName', ''),
                'company_id': row.get('CompanyId'),
                'original_index': idx
            }

            # Process individual item
            result = self.process_fallback_item(sku_info)
            result['original_index'] = idx
            result['sku_info'] = sku_info

            fallback_results.append(result)

            # Log progress every 5 items
            if (len(fallback_results) % 5 == 0) or (len(fallback_results) == len(fallback_df)):
                current_stats = self.rate_limiter.get_stats()
                logger.info(f"Progress: {len(fallback_results)}/{len(fallback_df)} items processed. "
                           f"Search requests in last minute: {current_stats['requests_last_minute']}")

        # Log summary
        successful_fallbacks = sum(1 for r in fallback_results if r.get('success', False))
        final_stats = self.rate_limiter.get_stats()
        logger.info(f"Fallback processing complete: {successful_fallbacks}/{len(fallback_results)} successful")
        logger.info(f"Final rate limiter stats: {final_stats['requests_last_minute']} requests in last minute")
        
        return fallback_results
    
    def _get_null_classification(self) -> Dict:
        """Get a null classification dictionary"""
        return {
            'Tier0': None,
            'Tier1': None,
            'Tier2': None
        }
    
    def get_rate_limiter_stats(self) -> Dict:
        """
        Get current rate limiter statistics

        Returns:
            Dictionary with rate limiter statistics
        """
        return self.rate_limiter.get_stats()

    def get_fallback_stats(self, fallback_results: List[Dict]) -> Dict:
        """
        Get statistics about fallback processing
        
        Args:
            fallback_results: List of fallback results
            
        Returns:
            Statistics dictionary
        """
        if not fallback_results:
            return {'total': 0, 'message': 'No fallback results to analyze'}
        
        total = len(fallback_results)
        successful = sum(1 for r in fallback_results if r.get('success', False))
        failed = total - successful
        
        # Count classifications that are still null after fallback
        still_null = sum(1 for r in fallback_results
                        if r.get('success', False) and
                        self.classifier.is_null_classification(r.get('classification', {})))
        
        # Count search results
        total_search_results = sum(r.get('search_results_count', 0) for r in fallback_results)
        avg_search_results = total_search_results / total if total > 0 else 0
        
        return {
            'total_processed': total,
            'successful_searches': successful,
            'failed_searches': failed,
            'still_null_after_fallback': still_null,
            'fallback_success_rate': successful / total if total > 0 else 0,
            'classification_improvement_rate': (successful - still_null) / total if total > 0 else 0,
            'average_search_results': avg_search_results,
            'total_search_results': total_search_results
        }
