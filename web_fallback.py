"""
Web Search Fallback System for SKU Categorisation Pipeline
Handles web search-based fallback for uncertain classifications
"""
import requests
import logging
import time
from typing import List, Dict, Optional
from urllib.parse import quote_plus
from bs4 import BeautifulSoup
import pandas as pd
from config import Config
from unified_classifier import UnifiedClassifier

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class WebFallbackProcessor:
    """Handles web search-based fallback processing for uncertain SKU classifications"""
    
    def __init__(self, config: Config = None):
        self.config = config or Config()
        self.classifier = UnifiedClassifier(config)
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })
    
    def construct_search_query(self, sku_name: str, company_name: str) -> str:
        """
        Construct search query for SKU
        
        Args:
            sku_name: Name of the SKU
            company_name: Company name
            
        Returns:
            Formatted search query
        """
        # Clean and format the search terms
        sku_clean = sku_name.strip().replace('"', '')
        company_clean = company_name.strip().replace('"', '')
        
        # Create site-specific search
        sites = ' OR '.join(f'site:{site}' for site in self.config.SEARCH_SITES)
        
        query = f'"{sku_clean}" {company_clean} ({sites})'
        return query
    
    def search_google(self, query: str, max_results: int = None) -> List[Dict]:
        """
        Perform Google search (simplified version - in production use proper search API)
        
        Args:
            query: Search query
            max_results: Maximum number of results
            
        Returns:
            List of search result dictionaries
        """
        max_results = max_results or self.config.MAX_SEARCH_RESULTS
        
        # Note: This is a simplified implementation
        # In production, use Google Custom Search API or similar service
        try:
            search_url = f"https://www.google.com/search?q={quote_plus(query)}&num={max_results}"
            
            response = self.session.get(search_url, timeout=10)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.content, 'html.parser')
            results = []
            
            # Extract search results (simplified parsing)
            for result_div in soup.find_all('div', class_='g')[:max_results]:
                title_elem = result_div.find('h3')
                link_elem = result_div.find('a')
                snippet_elem = result_div.find('span', class_='aCOpRe')
                
                if title_elem and link_elem:
                    result = {
                        'title': title_elem.get_text(strip=True),
                        'url': link_elem.get('href', ''),
                        'snippet': snippet_elem.get_text(strip=True) if snippet_elem else ''
                    }
                    results.append(result)
            
            return results
            
        except Exception as e:
            logger.error(f"Search failed for query '{query}': {str(e)}")
            return []
    
    def extract_product_info(self, search_results: List[Dict]) -> str:
        """
        Extract relevant product information from search results
        
        Args:
            search_results: List of search result dictionaries
            
        Returns:
            Formatted product information text
        """
        if not search_results:
            return "No search results found."
        
        info_parts = ["Search results:"]
        
        for i, result in enumerate(search_results, 1):
            title = result.get('title', 'No title')
            snippet = result.get('snippet', 'No description')
            url = result.get('url', '')
            
            info_parts.append(f"{i}. {title}")
            if snippet:
                info_parts.append(f"   Description: {snippet}")
            if url:
                info_parts.append(f"   URL: {url}")
            info_parts.append("")  # Empty line for separation
        
        return "\n".join(info_parts)
    
    def create_fallback_prompt(self, sku_info: Dict, search_info: str) -> str:
        """
        Create enhanced prompt with search results for fallback classification
        
        Args:
            sku_info: SKU information dictionary
            search_info: Formatted search results
            
        Returns:
            Enhanced user prompt
        """
        prompt = f"""SKU to classify:
- Company: {sku_info['company_name']}
- SKU: {sku_info['sku_name']}
- Primary: {sku_info['primary_category']}
- Secondary: {sku_info['secondary_category']}

{search_info}

Based on the SKU details and search results above, classify this product."""
        
        return prompt
    
    def process_fallback_item(self, sku_info: Dict) -> Dict:
        """
        Process a single SKU through web search fallback
        
        Args:
            sku_info: SKU information dictionary
            
        Returns:
            Classification result dictionary
        """
        try:
            # Construct search query
            query = self.construct_search_query(
                sku_info['sku_name'], 
                sku_info['company_name']
            )
            
            # Perform search
            search_results = self.search_google(query)
            
            if not search_results:
                logger.warning(f"No search results for SKU: {sku_info['sku_name']}")
                return {
                    'classification': self._get_null_classification(),
                    'source': 'web_fallback_failed',
                    'search_query': query,
                    'search_results_count': 0,
                    'success': False
                }
            
            # Extract product information
            search_info = self.extract_product_info(search_results)
            
            # Create enhanced prompt
            fallback_prompt = self.create_fallback_prompt(sku_info, search_info)
            
            # Enhanced system prompt for fallback
            enhanced_system_prompt = self.config.SYSTEM_PROMPT + "\n\nHere are relevant search results. Now try classifying based on both the SKU details and the search information."

            # Call classifier with enhanced information
            response = self.classifier.call_api(enhanced_system_prompt, fallback_prompt)

            # Parse single classification (not array)
            classifications = self.classifier.parse_response(f"[{response}]", 1)
            classification = classifications[0] if classifications else self._get_null_classification()
            
            return {
                'classification': classification,
                'source': 'web_fallback',
                'search_query': query,
                'search_results_count': len(search_results),
                'search_results': search_results,
                'raw_response': response,
                'success': True
            }
            
        except Exception as e:
            logger.error(f"Fallback processing failed for SKU {sku_info['sku_name']}: {str(e)}")
            return {
                'classification': self._get_null_classification(),
                'source': 'web_fallback_error',
                'error': str(e),
                'success': False
            }
    
    def process_fallback_batch(self, fallback_df: pd.DataFrame) -> List[Dict]:
        """
        Process a batch of SKUs through web search fallback
        
        Args:
            fallback_df: DataFrame with SKUs needing fallback processing
            
        Returns:
            List of fallback results
        """
        if fallback_df.empty:
            logger.info("No items to process in fallback")
            return []
        
        logger.info(f"Processing {len(fallback_df)} items through web search fallback")
        
        fallback_results = []
        
        for idx, row in fallback_df.iterrows():
            sku_info = {
                'sku_id': row.get('SKUId'),
                'sku_name': row.get('SKUName', ''),
                'company_name': row.get('CompanyName', ''),
                'primary_category': row.get('PrimaryCategoryName', ''),
                'secondary_category': row.get('SecondaryCategoryName', ''),
                'company_id': row.get('CompanyId'),
                'original_index': idx
            }
            
            # Process individual item
            result = self.process_fallback_item(sku_info)
            result['original_index'] = idx
            result['sku_info'] = sku_info
            
            fallback_results.append(result)
            
            # Add delay to avoid rate limiting
            time.sleep(0.5)
        
        # Log summary
        successful_fallbacks = sum(1 for r in fallback_results if r.get('success', False))
        logger.info(f"Fallback processing complete: {successful_fallbacks}/{len(fallback_results)} successful")
        
        return fallback_results
    
    def _get_null_classification(self) -> Dict:
        """Get a null classification dictionary"""
        return {
            'Tier0': None,
            'Tier1': None,
            'Tier2': None
        }
    
    def get_fallback_stats(self, fallback_results: List[Dict]) -> Dict:
        """
        Get statistics about fallback processing
        
        Args:
            fallback_results: List of fallback results
            
        Returns:
            Statistics dictionary
        """
        if not fallback_results:
            return {'total': 0, 'message': 'No fallback results to analyze'}
        
        total = len(fallback_results)
        successful = sum(1 for r in fallback_results if r.get('success', False))
        failed = total - successful
        
        # Count classifications that are still null after fallback
        still_null = sum(1 for r in fallback_results
                        if r.get('success', False) and
                        self.classifier.is_null_classification(r.get('classification', {})))
        
        # Count search results
        total_search_results = sum(r.get('search_results_count', 0) for r in fallback_results)
        avg_search_results = total_search_results / total if total > 0 else 0
        
        return {
            'total_processed': total,
            'successful_searches': successful,
            'failed_searches': failed,
            'still_null_after_fallback': still_null,
            'fallback_success_rate': successful / total if total > 0 else 0,
            'classification_improvement_rate': (successful - still_null) / total if total > 0 else 0,
            'average_search_results': avg_search_results,
            'total_search_results': total_search_results
        }
