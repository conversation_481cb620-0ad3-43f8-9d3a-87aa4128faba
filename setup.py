"""
Setup script for SKU Categorisation Pipeline
Handles initial setup and validation
"""
import os
import sys
from pathlib import Path
import subprocess

def create_directories():
    """Create necessary directories"""
    directories = ['data', 'cache', 'logs']
    
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
        print(f"✓ Created directory: {directory}")

def check_python_version():
    """Check Python version compatibility"""
    if sys.version_info < (3, 8):
        print("❌ Python 3.8 or higher is required")
        return False
    
    print(f"✓ Python version: {sys.version}")
    return True

def install_dependencies():
    """Install required dependencies"""
    try:
        print("Installing dependencies...")
        subprocess.check_call([sys.executable, '-m', 'pip', 'install', '-r', 'requirements.txt'])
        print("✓ Dependencies installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install dependencies: {e}")
        return False

def setup_environment():
    """Set up environment file"""
    env_file = Path('.env')
    env_example = Path('.env.example')
    
    if not env_file.exists() and env_example.exists():
        # Copy example to .env
        with open(env_example, 'r') as src, open(env_file, 'w') as dst:
            dst.write(src.read())
        print("✓ Created .env file from template")
        print("⚠️  Please edit .env file and add your OpenAI API key")
        return False
    elif env_file.exists():
        print("✓ .env file already exists")
        return True
    else:
        print("❌ No .env.example file found")
        return False

def validate_config():
    """Validate configuration"""
    try:
        from config import Config
        config = Config()

        # Check if at least one API key is configured
        has_openai = bool(config.OPENAI_API_KEY)
        has_gemini = bool(config.GEMINI_API_KEY)

        if not has_openai and not has_gemini:
            print("⚠️  No API keys configured. Please set OPENAI_API_KEY or GEMINI_API_KEY")
            return False

        # Validate based on selected provider
        if config.MODEL_PROVIDER == 'openai' and not has_openai:
            print("⚠️  OpenAI API key required for selected provider")
            return False

        if config.MODEL_PROVIDER == 'gemini' and not has_gemini:
            print("⚠️  Gemini API key required for selected provider")
            return False

        print(f"✓ Configuration validated for {config.MODEL_PROVIDER} provider")
        return True
    except Exception as e:
        print(f"❌ Configuration validation failed: {e}")
        return False

def generate_sample_data():
    """Generate sample data for testing"""
    try:
        from generate_sample_data import create_sample_files
        create_sample_files()
        print("✓ Sample data generated")
        return True
    except Exception as e:
        print(f"❌ Failed to generate sample data: {e}")
        return False

def run_tests():
    """Run basic tests"""
    try:
        from test_pipeline import run_tests
        success = run_tests()
        if success:
            print("✓ All tests passed")
        else:
            print("⚠️  Some tests failed")
        return success
    except Exception as e:
        print(f"❌ Failed to run tests: {e}")
        return False

def main():
    """Main setup function"""
    print("SKU Categorisation Pipeline Setup")
    print("=" * 40)
    
    # Check Python version
    if not check_python_version():
        sys.exit(1)
    
    # Create directories
    create_directories()
    
    # Install dependencies
    if not install_dependencies():
        print("❌ Setup failed: Could not install dependencies")
        sys.exit(1)
    
    # Setup environment
    env_ready = setup_environment()
    
    # Generate sample data
    generate_sample_data()
    
    print("\n" + "=" * 40)
    print("Setup Summary:")
    print("✓ Directories created")
    print("✓ Dependencies installed")
    print("✓ Sample data generated")
    
    if env_ready:
        # Validate configuration
        if validate_config():
            print("✓ Configuration validated")
            
            # Run tests
            print("\nRunning tests...")
            if run_tests():
                print("\n🎉 Setup completed successfully!")
                print("\nNext steps:")
                print("1. Review the generated sample data in the 'data' directory")
                print("2. Run the pipeline: python main.py --input data/SKU_Categorisation_Master.xlsx")
            else:
                print("\n⚠️  Setup completed with test warnings")
                print("The pipeline should still work, but please check the test output")
        else:
            print("⚠️  Please set your API keys in the .env file")
            print("- For OpenAI: Set OPENAI_API_KEY")
            print("- For Gemini: Set GEMINI_API_KEY")
            print("Then run: python main.py --input data/SKU_Categorisation_Master.xlsx")
    else:
        print("⚠️  Please edit the .env file and add your API keys")
        print("- For OpenAI: Set OPENAI_API_KEY")
        print("- For Gemini: Set GEMINI_API_KEY")
        print("Then run the setup again or start using the pipeline directly")
    
    print("\nFor help, run: python main.py --help")
    print("For documentation, see: README.md")

if __name__ == "__main__":
    main()
