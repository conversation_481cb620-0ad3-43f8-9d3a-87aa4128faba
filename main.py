"""
Main Execution Script for SKU Categorisation Pipeline
Orchestrates the complete SKU classification process
"""
import logging
import sys
import argparse
from datetime import datetime
from pathlib import Path
from typing import Optional

# Import pipeline modules
from config import Config
from input_processor import InputProcessor
from batch_processor import BatchProcessor
from unified_classifier import UnifiedClassifier
from null_detector import NullDetector
from web_fallback import WebFallbackProcessor
from output_processor import OutputProcessor
from cache_manager import CacheManager

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('logs/pipeline.log', mode='a')
    ]
)
logger = logging.getLogger(__name__)

class SKUCategorizationPipeline:
    """Main pipeline orchestrator for SKU categorisation"""
    
    def __init__(self, config: Config = None):
        self.config = config or Config()
        
        # Initialize components
        self.input_processor = InputProcessor(self.config)
        self.batch_processor = BatchProcessor(self.config)
        self.classifier = UnifiedClassifier(self.config)
        self.null_detector = NullDetector(self.config)
        self.web_fallback = WebFallbackProcessor(self.config) if self.config.ENABLE_WEB_FALLBACK else None
        self.output_processor = OutputProcessor(self.config)
        self.cache_manager = CacheManager(self.config) if self.config.ENABLE_CACHING else None
        
        # Log model information
        model_info = self.classifier.get_model_info()
        logger.info("="*60)
        logger.info(f"🤖 ACTIVE MODEL: {model_info['provider'].upper()} ({model_info['model']})")
        logger.info("="*60)
    
    def run_pipeline(self, input_file: str = None, output_file: str = None) -> str:
        """
        Run the complete SKU categorisation pipeline
        
        Args:
            input_file: Path to input file (optional, uses config default)
            output_file: Path to output file (optional, uses config default)
            
        Returns:
            Path to output file
        """
        start_time = datetime.now()
        logger.info(f"Starting SKU Categorisation Pipeline at {start_time}")
        
        try:
            # Step 1: Input Preparation
            logger.info("Step 1: Loading and preparing input data")
            df, metadata = self.input_processor.process_input_file(input_file)
            logger.info(f"Loaded {metadata['processed_rows']} SKUs for processing")
            
            # Step 2: Batch Processing with GPT
            logger.info("Step 2: Processing batches with GPT")
            batch_results = self._process_batches(df)
            
            # Step 3: Detect Null Classifications
            logger.info("Step 3: Detecting null classifications")
            null_detection = self.null_detector.detect_null_classifications(batch_results)
            
            # Step 4: Web Search Fallback (if enabled and needed)
            fallback_results = []
            if self.web_fallback and null_detection['null_items']:
                logger.info("Step 4: Processing fallback items with web search")
                fallback_df = self.null_detector.prepare_fallback_data(
                    null_detection['null_items'], df
                )
                fallback_results = self.web_fallback.process_fallback_batch(fallback_df)
            
            # Step 5: Consolidate Results
            logger.info("Step 5: Consolidating results")
            model_info = self.classifier.get_model_info()
            final_df = self.output_processor.consolidate_results(
                df, batch_results, fallback_results, model_info
            )
            
            # Step 6: Generate Output
            logger.info("Step 6: Generating output files")
            output_path = self.output_processor.save_results(final_df, output_file)
            
            # Step 7: Save Processing Log
            summary_stats = self.output_processor.generate_summary_stats(final_df)
            log_path = self.output_processor.save_processing_log(
                batch_results, fallback_results, summary_stats
            )
            
            # Cache results if enabled
            if self.cache_manager:
                self.cache_manager.cache_batch_results(batch_results)
                if fallback_results:
                    self.cache_manager.cache_fallback_results(fallback_results)
            
            # Final summary
            end_time = datetime.now()
            duration = end_time - start_time
            
            logger.info("="*60)
            logger.info("PIPELINE COMPLETED SUCCESSFULLY")
            logger.info(f"Duration: {duration}")
            logger.info(f"Total SKUs processed: {summary_stats['total_skus']}")
            logger.info(f"Complete classifications: {summary_stats['complete_classifications']}")
            logger.info(f"Completion rate: {summary_stats['completion_rate']:.2%}")
            logger.info(f"Output file: {output_path}")
            logger.info(f"Processing log: {log_path}")
            logger.info("="*60)
            
            return output_path
            
        except Exception as e:
            logger.error(f"Pipeline failed: {str(e)}", exc_info=True)
            raise
    
    def _process_batches(self, df) -> list:
        """Process SKU data in batches using GPT"""
        
        # Check cache for existing classifications
        if self.cache_manager:
            logger.info("Checking cache for existing classifications")
            # This would require modifying batch processing to handle cached items
            # For now, we'll process all items through GPT
        
        # Process batches
        def classify_batch(batch_data, batch_metadata):
            return self.classifier.classify_batch(batch_data, batch_metadata)
        
        batch_results = self.batch_processor.process_batches_sequentially(
            df, classify_batch
        )
        
        # Validate results
        validation = self.batch_processor.validate_batch_results(batch_results, df)
        if not validation['alignment_valid']:
            logger.warning("Batch result alignment issues detected")
        
        # Log statistics
        stats = self.classifier.get_classification_stats(batch_results)
        logger.info(f"{stats['provider'].upper()} Classification Stats: {stats['successful_classifications']}/{stats['total_skus']} successful")
        
        return batch_results
    
    def validate_configuration(self) -> bool:
        """Validate pipeline configuration"""
        try:
            self.config.validate()
            logger.info("Configuration validation passed")
            return True
        except Exception as e:
            logger.error(f"Configuration validation failed: {str(e)}")
            return False

def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(description='SKU Categorisation Pipeline')
    parser.add_argument('--input', '-i', help='Input file path')
    parser.add_argument('--output', '-o', help='Output file path')
    parser.add_argument('--config', '-c', help='Configuration file path')
    parser.add_argument('--batch-size', type=int, help='Batch size for processing')
    parser.add_argument('--model', choices=['openai', 'gemini'], help='Model provider to use')
    parser.add_argument('--no-fallback', action='store_true', help='Disable web search fallback')
    parser.add_argument('--no-cache', action='store_true', help='Disable caching')
    
    args = parser.parse_args()
    
    try:
        # Initialize configuration
        config = Config()
        
        # Override config with command line arguments
        if args.batch_size:
            config.BATCH_SIZE = args.batch_size
        if args.model:
            config.MODEL_PROVIDER = args.model
        if args.no_fallback:
            config.ENABLE_WEB_FALLBACK = False
        if args.no_cache:
            config.ENABLE_CACHING = False
        
        # Initialize and run pipeline
        pipeline = SKUCategorizationPipeline(config)
        
        if not pipeline.validate_configuration():
            sys.exit(1)
        
        output_path = pipeline.run_pipeline(args.input, args.output)
        print(f"Pipeline completed successfully. Output saved to: {output_path}")
        
    except KeyboardInterrupt:
        logger.info("Pipeline interrupted by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Pipeline failed: {str(e)}", exc_info=True)
        sys.exit(1)

if __name__ == "__main__":
    main()
