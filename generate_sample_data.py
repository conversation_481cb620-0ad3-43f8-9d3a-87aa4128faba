"""
Sample Data Generator for SKU Categorisation Pipeline
Creates realistic sample data for testing the pipeline
"""
import pandas as pd
import random
from pathlib import Path

def generate_sample_sku_data(num_skus: int = 50) -> pd.DataFrame:
    """
    Generate sample SKU data for testing
    
    Args:
        num_skus: Number of SKUs to generate
        
    Returns:
        DataFrame with sample SKU data
    """
    
    # Sample companies and their typical products
    companies_products = {
        'Dabur Ltd': [
            ('Dabur Red Paste 100g', 'Personal Care', 'Oral Care'),
            ('Dabur Honey 500g', 'Food & Beverages', 'Honey'),
            ('Dabur Chyawanprash 1kg', 'Health & Wellness', 'Ayurvedic'),
            ('Dabur Amla Hair Oil 200ml', 'Personal Care', 'Hair Care'),
            ('Dabur Gulabari Rose Water 120ml', 'Personal Care', 'Skin Care')
        ],
        'Hindustan Unilever Ltd': [
            ('Surf Excel Easy Wash 1kg', 'Home Care', 'Detergent'),
            ('Dove Beauty Bar 75g', 'Personal Care', 'Bath & Body'),
            ('Lipton Green Tea 25 Bags', 'Food & Beverages', 'Tea'),
            ('Clinic Plus Shampoo 340ml', 'Personal Care', 'Hair Care'),
            ('Vim Dishwash Gel 500ml', 'Home Care', 'Dishwash')
        ],
        'Nestle India Ltd': [
            ('Maggi 2-Minute Noodles 70g', 'Food & Beverages', 'Instant Food'),
            ('Nescafe Classic Coffee 50g', 'Food & Beverages', 'Coffee'),
            ('KitKat Chocolate 37.3g', 'Food & Beverages', 'Confectionery'),
            ('Cerelac Baby Food 300g', 'Baby Care', 'Baby Food'),
            ('Milkmaid Condensed Milk 400g', 'Food & Beverages', 'Dairy')
        ],
        'Britannia Industries Ltd': [
            ('Britannia Good Day Cookies 100g', 'Food & Beverages', 'Biscuits'),
            ('Britannia Bread 400g', 'Food & Beverages', 'Bakery'),
            ('Britannia Marie Gold 250g', 'Food & Beverages', 'Biscuits'),
            ('Britannia Cake Rusk 200g', 'Food & Beverages', 'Bakery'),
            ('Britannia Cheese Slices 200g', 'Food & Beverages', 'Dairy')
        ],
        'ITC Ltd': [
            ('Aashirvaad Atta 5kg', 'Food & Beverages', 'Flour & Grains'),
            ('Sunfeast Dark Fantasy 300g', 'Food & Beverages', 'Biscuits'),
            ('Bingo Mad Angles 90g', 'Food & Beverages', 'Snacks'),
            ('Fiama Di Wills Shower Gel 250ml', 'Personal Care', 'Bath & Body'),
            ('Vivel Soap 100g', 'Personal Care', 'Bath & Body')
        ],
        'Patanjali Ayurved Ltd': [
            ('Patanjali Kesh Kanti Shampoo 200ml', 'Personal Care', 'Hair Care'),
            ('Patanjali Dant Kanti Toothpaste 100g', 'Personal Care', 'Oral Care'),
            ('Patanjali Ghee 500ml', 'Food & Beverages', 'Dairy'),
            ('Patanjali Atta Noodles 60g', 'Food & Beverages', 'Instant Food'),
            ('Patanjali Aloe Vera Gel 150ml', 'Personal Care', 'Skin Care')
        ],
        'Godrej Consumer Products Ltd': [
            ('Godrej No.1 Soap 100g', 'Personal Care', 'Bath & Body'),
            ('Good Knight Mosquito Coil 10pc', 'Home Care', 'Pest Control'),
            ('Godrej Hair Dye 20ml', 'Personal Care', 'Hair Care'),
            ('Hit Spray 400ml', 'Home Care', 'Pest Control'),
            ('Cinthol Soap 100g', 'Personal Care', 'Bath & Body')
        ],
        'Marico Ltd': [
            ('Parachute Coconut Oil 500ml', 'Personal Care', 'Hair Care'),
            ('Saffola Gold Oil 1L', 'Food & Beverages', 'Cooking Oil'),
            ('Set Wet Hair Gel 100ml', 'Personal Care', 'Hair Care'),
            ('Livon Hair Serum 50ml', 'Personal Care', 'Hair Care'),
            ('True Elements Oats 500g', 'Food & Beverages', 'Breakfast Cereals')
        ],
        'Ashika Incense Inc': [
            ('SHARAN 2 IN 1 - 40g (BIG)', 'Incense sticks', 'SHARAN'),
            ('Cycle Pure Agarbatti 100g', 'Incense sticks', 'Traditional'),
            ('Moksh Dhoop Sticks 50g', 'Incense sticks', 'Dhoop'),
            ('Divine Flora Incense 75g', 'Incense sticks', 'Floral'),
            ('Sacred Sandalwood 60g', 'Incense sticks', 'Sandalwood')
        ],
        'Emami Ltd': [
            ('Boroplus Antiseptic Cream 40ml', 'Personal Care', 'Skin Care'),
            ('Navratna Hair Oil 200ml', 'Personal Care', 'Hair Care'),
            ('Fair & Handsome Cream 60g', 'Personal Care', 'Skin Care'),
            ('Zandu Balm 25ml', 'Health & Wellness', 'Pain Relief'),
            ('Kesh King Hair Oil 120ml', 'Personal Care', 'Hair Care')
        ]
    }
    
    # Generate sample data
    sample_data = []
    sku_id = 1000
    company_id = 100
    
    company_id_map = {}
    
    for company, products in companies_products.items():
        if company not in company_id_map:
            company_id_map[company] = company_id
            company_id += 1
        
        # Generate multiple variations of each product
        for product_name, primary_cat, secondary_cat in products:
            # Create 2-3 variations per product
            variations = [
                product_name,
                product_name.replace('100g', '200g').replace('50g', '100g'),
                product_name.replace('1kg', '500g').replace('500ml', '1L')
            ]
            
            for variation in variations[:random.randint(1, 2)]:
                sample_data.append({
                    'SKUId': sku_id,
                    'SKUName': variation,
                    'PrimaryCategoryName': primary_cat,
                    'SecondaryCategoryName': secondary_cat,
                    'CompanyId': company_id_map[company],
                    'CompanyName': company
                })
                sku_id += 1
                
                if len(sample_data) >= num_skus:
                    break
            
            if len(sample_data) >= num_skus:
                break
        
        if len(sample_data) >= num_skus:
            break
    
    # Convert to DataFrame and shuffle
    df = pd.DataFrame(sample_data[:num_skus])
    df = df.sample(frac=1).reset_index(drop=True)
    
    return df

def create_sample_files():
    """Create sample input files for testing"""
    
    # Ensure data directory exists
    Path('data').mkdir(exist_ok=True)
    
    # Generate different sized datasets (CSV as primary format)
    datasets = {
        'SKU_Categorisation_Master_Small.csv': 25,
        'SKU_Categorisation_Master.csv': 50,
        'SKU_Categorisation_Master_Large.csv': 100
    }

    # Create CSV files (primary format)
    for filename, size in datasets.items():
        df = generate_sample_sku_data(size)
        filepath = Path('data') / filename
        df.to_csv(filepath, index=False, encoding='utf-8')
        print(f"Created {filepath} with {len(df)} SKUs")

    # Also create Excel versions for backward compatibility
    for filename, size in datasets.items():
        df = generate_sample_sku_data(size)
        excel_filename = filename.replace('.csv', '.xlsx')
        filepath = Path('data') / excel_filename
        df.to_excel(filepath, index=False)
        print(f"Created {filepath} with {len(df)} SKUs (Excel format for compatibility)")

if __name__ == "__main__":
    create_sample_files()
    print("Sample data generation completed!")
