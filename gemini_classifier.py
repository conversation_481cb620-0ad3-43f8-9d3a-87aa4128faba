"""
Gemini Classification Module for SKU Categorisation Pipeline
Handles Google Gemini API calls, prompt construction, and response parsing
"""
import json
import re
import logging
import time
from typing import List, Dict, Optional, Tuple
import google.generativeai as genai
from retry import retry
from config import Config

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class GeminiClassifier:
    """Handles Gemini-based SKU classification"""
    
    def __init__(self, config: Config = None):
        self.config = config or Config()
        
        # Configure Gemini
        genai.configure(api_key=self.config.GEMINI_API_KEY)
        
        # Initialize model
        self.model = genai.GenerativeModel(
            model_name=self.config.GEMINI_MODEL,
            generation_config=genai.types.GenerationConfig(
                temperature=0.1,  # Low temperature for consistent results
                max_output_tokens=4000,  # Increased for larger batches
                candidate_count=1,
                top_p=0.8,
                top_k=40
            )
        )
        
    @retry(tries=3, delay=1, backoff=2)
    def call_gemini_api(self, system_prompt: str, user_prompt: str) -> str:
        """
        Make API call to Gemini with retry logic
        
        Args:
            system_prompt: System prompt for Gemini
            user_prompt: User prompt with SKU data
            
        Returns:
            Gemini response text
        """
        try:
            # Enhance system prompt for Gemini with explicit JSON formatting instructions
            enhanced_system_prompt = f"""{system_prompt}

CRITICAL: Your response must be a valid, complete JSON array. Each object must have exactly these fields:
- "Tier0": string or null
- "Tier1": string or null
- "Tier2": string or null

Example format:
[
  {{"Tier0": "Personal Care", "Tier1": "Toothpaste", "Tier2": "Whitening"}},
  {{"Tier0": "Food", "Tier1": "Snacks", "Tier2": "Chips"}}
]

Do not truncate the response. Complete all objects in the array."""

            # Combine enhanced system and user prompts for Gemini
            combined_prompt = f"{enhanced_system_prompt}\n\n{user_prompt}"
            
            response = self.model.generate_content(combined_prompt)

            if response.candidates and response.candidates[0].content:
                response_text = response.candidates[0].content.parts[0].text.strip()

                # Check for potential truncation
                if self._is_response_truncated(response_text):
                    logger.warning("Response appears to be truncated, attempting recovery")
                    # Log the truncated response for debugging
                    logger.debug(f"Truncated response: {response_text}")

                return response_text
            else:
                # Check if response was blocked or filtered
                if response.candidates and response.candidates[0].finish_reason:
                    finish_reason = response.candidates[0].finish_reason
                    logger.error(f"Gemini response blocked/filtered. Reason: {finish_reason}")
                    raise Exception(f"Gemini response blocked: {finish_reason}")
                else:
                    raise Exception("No valid response from Gemini")
            
        except Exception as e:
            logger.error(f"Gemini API call failed: {str(e)}")
            raise

    def _is_response_truncated(self, response: str) -> bool:
        """
        Check if the response appears to be truncated

        Args:
            response: Response text to check

        Returns:
            True if response appears truncated
        """
        # Check for common truncation indicators
        truncation_indicators = [
            # Incomplete JSON structures
            response.count('[') > response.count(']'),
            response.count('{') > response.count('}'),
            # Ends with incomplete field
            re.search(r'"[^"]*":\s*"[^"]*$', response.strip()),
            # Ends with comma (incomplete object)
            response.strip().endswith(','),
            # Very short response for what should be a JSON array
            len(response.strip()) < 50 and '[' in response
        ]

        return any(truncation_indicators)
    
    def clean_gemini_response(self, response: str) -> str:
        """
        Clean Gemini response to extract JSON

        Args:
            response: Raw Gemini response

        Returns:
            Cleaned JSON string
        """
        # Remove markdown code blocks if present
        response = re.sub(r'```json\s*', '', response)
        response = re.sub(r'```\s*', '', response)

        # Try to extract JSON array using regex
        json_pattern = r'\[.*\]'
        match = re.search(json_pattern, response, re.DOTALL)

        if match:
            json_str = match.group(0)
            # Try to fix incomplete JSON
            json_str = self._fix_incomplete_json(json_str)
            return json_str

        # If no array found, return original response
        return response.strip()

    def _fix_incomplete_json(self, json_str: str) -> str:
        """
        Attempt to fix incomplete JSON strings

        Args:
            json_str: Potentially incomplete JSON string

        Returns:
            Fixed JSON string
        """
        try:
            # First, try to parse as-is
            json.loads(json_str)
            return json_str
        except json.JSONDecodeError:
            # Try multiple fixing strategies
            fixed_json = json_str.strip()

            # Strategy 1: Remove trailing commas
            fixed_json = re.sub(r',\s*([}\]])', r'\1', fixed_json)

            # Strategy 2: Handle incomplete strings/values
            # Fix incomplete string values (ending with quote but no closing quote)
            fixed_json = re.sub(r'"([^"]*)":\s*"([^"]*?)$', r'"\1": "\2"', fixed_json)

            # Strategy 3: Handle incomplete objects
            # If we have an incomplete object (ends with comma or incomplete field)
            if re.search(r',\s*$', fixed_json) or re.search(r'"[^"]*":\s*$', fixed_json):
                # Remove trailing comma or incomplete field
                fixed_json = re.sub(r',\s*$', '', fixed_json)
                fixed_json = re.sub(r'"[^"]*":\s*$', '', fixed_json)
                # Remove trailing comma after cleanup
                fixed_json = re.sub(r',\s*$', '', fixed_json)

            # Strategy 4: Balance braces and brackets
            open_braces = fixed_json.count('{')
            close_braces = fixed_json.count('}')
            open_brackets = fixed_json.count('[')
            close_brackets = fixed_json.count(']')

            # Add missing closing braces
            while open_braces > close_braces:
                fixed_json += '}'
                close_braces += 1

            # Add missing closing brackets
            while open_brackets > close_brackets:
                fixed_json += ']'
                close_brackets += 1

            # Strategy 5: Try to parse the fixed JSON
            try:
                json.loads(fixed_json)
                return fixed_json
            except json.JSONDecodeError:
                # Strategy 6: Last resort - try to extract valid objects
                return self._extract_valid_objects(json_str)

    def _extract_valid_objects(self, json_str: str) -> str:
        """
        Extract valid JSON objects from malformed JSON string

        Args:
            json_str: Malformed JSON string

        Returns:
            JSON string with extracted valid objects
        """
        try:
            objects = []

            # Try to find complete objects using regex
            # Look for patterns like {"Tier0": "value", "Tier1": "value", "Tier2": "value"}
            complete_object_pattern = r'\{\s*"Tier0"\s*:\s*"[^"]*"\s*,\s*"Tier1"\s*:\s*"[^"]*"\s*,\s*"Tier2"\s*:\s*"[^"]*"\s*\}'
            complete_matches = re.findall(complete_object_pattern, json_str, re.DOTALL)

            for match in complete_matches:
                try:
                    obj = json.loads(match)
                    objects.append(obj)
                except:
                    continue

            # If no complete objects found, try to extract partial objects
            if not objects:
                # Look for any object-like structures
                partial_pattern = r'\{[^{}]*\}'
                partial_matches = re.findall(partial_pattern, json_str, re.DOTALL)

                for match in partial_matches:
                    try:
                        # Try to parse as-is
                        obj = json.loads(match)
                        if isinstance(obj, dict):
                            objects.append(obj)
                    except:
                        # Try to fix this individual object
                        fixed_obj = self._fix_single_object(match)
                        if fixed_obj:
                            objects.append(fixed_obj)

            # If still no objects, create a null one
            if not objects:
                objects = [self._get_null_classification()]

            return json.dumps(objects)

        except Exception:
            # Ultimate fallback
            return json.dumps([self._get_null_classification()])

    def _aggressive_json_fix(self, json_str: str, expected_count: int) -> str:
        """
        Aggressively attempt to fix malformed JSON by reconstructing it

        Args:
            json_str: Malformed JSON string
            expected_count: Expected number of classifications

        Returns:
            Reconstructed JSON string
        """
        try:
            # Extract any valid JSON objects we can find
            objects = []

            # Look for individual objects using regex
            object_pattern = r'\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\}'
            matches = re.findall(object_pattern, json_str, re.DOTALL)

            for match in matches:
                try:
                    # Try to parse each potential object
                    obj = json.loads(match)
                    if isinstance(obj, dict):
                        objects.append(obj)
                except:
                    # Try to fix this individual object
                    fixed_obj = self._fix_single_object(match)
                    if fixed_obj:
                        objects.append(fixed_obj)

            # If we don't have enough objects, create null ones
            while len(objects) < expected_count:
                objects.append(self._get_null_classification())

            # Return as JSON array
            return json.dumps(objects[:expected_count])

        except Exception:
            # Last resort: create null classifications
            null_classifications = [self._get_null_classification() for _ in range(expected_count)]
            return json.dumps(null_classifications)

    def _fix_single_object(self, obj_str: str) -> Dict:
        """
        Try to fix a single JSON object

        Args:
            obj_str: String representation of a JSON object

        Returns:
            Fixed dictionary or None if unfixable
        """
        try:
            # Remove trailing commas
            fixed = re.sub(r',\s*}', '}', obj_str)

            # Try to extract key-value pairs
            tier_pattern = r'"(Tier[0-2])"\s*:\s*"([^"]*)"'
            matches = re.findall(tier_pattern, fixed)

            result = self._get_null_classification()
            for key, value in matches:
                if key in result:
                    result[key] = value

            return result

        except Exception:
            return None
    
    def parse_gemini_response(self, response: str, expected_count: int) -> List[Dict]:
        """
        Parse Gemini response into structured classifications
        
        Args:
            response: Gemini response text
            expected_count: Expected number of classifications
            
        Returns:
            List of classification dictionaries
        """
        try:
            # Clean the response
            cleaned_response = self.clean_gemini_response(response)
            
            # Parse JSON
            classifications = json.loads(cleaned_response)
            
            # Validate structure
            if not isinstance(classifications, list):
                raise ValueError("Response is not a JSON array")
            
            # Ensure we have the expected number of results
            if len(classifications) != expected_count:
                logger.warning(f"Expected {expected_count} classifications, got {len(classifications)}")
            
            # Validate each classification
            validated_classifications = []
            for i, classification in enumerate(classifications):
                validated = self._validate_classification(classification, i)
                validated_classifications.append(validated)
            
            return validated_classifications
            
        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse JSON response: {str(e)}")
            logger.error(f"Original response was: {response}")
            logger.error(f"Cleaned response was: {cleaned_response}")

            # Try one more time with aggressive fixing
            try:
                fixed_response = self._aggressive_json_fix(cleaned_response, expected_count)
                classifications = json.loads(fixed_response)
                logger.warning(f"Successfully recovered with aggressive JSON fixing")

                # Validate and return
                validated_classifications = []
                for classification in classifications[:expected_count]:
                    validated = self._validate_classification(classification)
                    validated_classifications.append(validated)

                return validated_classifications

            except Exception as recovery_error:
                logger.error(f"Aggressive JSON fix also failed: {str(recovery_error)}")
                # Return null classifications for all expected items
                return [self._get_null_classification() for _ in range(expected_count)]
        
        except Exception as e:
            logger.error(f"Error parsing Gemini response: {str(e)}")
            return [self._get_null_classification() for _ in range(expected_count)]
    
    def _validate_classification(self, classification: Dict, index: int) -> Dict:
        """
        Validate and normalize a single classification
        
        Args:
            classification: Classification dictionary
            index: Index for error reporting
            
        Returns:
            Validated classification dictionary
        """
        required_fields = ['Tier0', 'Tier1', 'Tier2']
        
        # Ensure all required fields exist
        for field in required_fields:
            if field not in classification:
                logger.warning(f"Missing field '{field}' in classification {index}")
                classification[field] = None
        
        # Normalize null values
        for field in required_fields:
            value = classification[field]
            if value is None or str(value).strip().lower() in ['null', '', 'none']:
                classification[field] = None
        
        return classification
    
    def _get_null_classification(self) -> Dict:
        """Get a null classification dictionary"""
        return {
            'Tier0': None,
            'Tier1': None,
            'Tier2': None
        }

    def _suggest_batch_size_reduction(self, current_batch_size: int, truncation_count: int) -> int:
        """
        Suggest a reduced batch size when truncation is detected

        Args:
            current_batch_size: Current batch size
            truncation_count: Number of truncations detected

        Returns:
            Suggested new batch size
        """
        if truncation_count > 2:
            # Significant truncation, reduce by 50%
            new_size = max(1, current_batch_size // 2)
            logger.warning(f"High truncation rate detected. Consider reducing batch size from {current_batch_size} to {new_size}")
            return new_size
        elif truncation_count > 0:
            # Some truncation, reduce by 25%
            new_size = max(1, int(current_batch_size * 0.75))
            logger.info(f"Truncation detected. Consider reducing batch size from {current_batch_size} to {new_size}")
            return new_size

        return current_batch_size
    
    def classify_batch(self, batch_data: List[Dict], batch_metadata: Dict) -> Dict:
        """
        Classify a batch of SKUs using Gemini
        
        Args:
            batch_data: List of SKU information dictionaries
            batch_metadata: Batch metadata
            
        Returns:
            Dictionary with classification results
        """
        batch_num = batch_metadata.get('batch_number', 0)
        logger.info(f"Classifying batch {batch_num} with {len(batch_data)} SKUs using Gemini")
        
        try:
            # Create user prompt
            user_prompt = self._create_batch_user_prompt(batch_data)
            
            # Call Gemini API
            response = self.call_gemini_api(self.config.SYSTEM_PROMPT, user_prompt)
            
            # Parse response
            classifications = self.parse_gemini_response(response, len(batch_data))
            
            # Create result
            result = {
                'batch_number': batch_num,
                'batch_metadata': batch_metadata,
                'classifications': classifications,
                'raw_response': response,
                'success': True,
                'timestamp': time.time(),
                'model_used': 'gemini'
            }
            
            logger.info(f"Successfully classified batch {batch_num} with Gemini")
            return result
            
        except Exception as e:
            logger.error(f"Error classifying batch {batch_num} with Gemini: {str(e)}")
            
            # Return error result with null classifications
            return {
                'batch_number': batch_num,
                'batch_metadata': batch_metadata,
                'classifications': [self._get_null_classification() for _ in batch_data],
                'error': str(e),
                'success': False,
                'timestamp': time.time(),
                'model_used': 'gemini'
            }
    
    def _create_batch_user_prompt(self, batch_data: List[Dict]) -> str:
        """
        Create user prompt for a batch of SKUs
        
        Args:
            batch_data: List of SKU information dictionaries
            
        Returns:
            Formatted user prompt
        """
        prompt_lines = ["SKUs:"]
        
        for sku in batch_data:
            line = (f"- Company: {sku['company_name']}, "
                   f"SKU: {sku['sku_name']}, "
                   f"Primary: {sku['primary_category']}, "
                   f"Secondary: {sku['secondary_category']}")
            prompt_lines.append(line)
        
        return "\n".join(prompt_lines)
    
    def is_null_classification(self, classification: Dict) -> bool:
        """
        Check if a classification is null/uncertain
        
        Args:
            classification: Classification dictionary
            
        Returns:
            True if classification is null/uncertain
        """
        required_fields = ['Tier0', 'Tier1', 'Tier2']
        
        return all(
            classification.get(field) is None or 
            str(classification.get(field)).strip().lower() in ['null', '', 'none']
            for field in required_fields
        )
    
    def get_classification_stats(self, batch_results: List[Dict]) -> Dict:
        """
        Get statistics about classification results
        
        Args:
            batch_results: List of batch results
            
        Returns:
            Statistics dictionary
        """
        total_skus = 0
        successful_classifications = 0
        null_classifications = 0
        failed_batches = 0
        
        for result in batch_results:
            if result.get('success', False):
                classifications = result.get('classifications', [])
                total_skus += len(classifications)
                
                for classification in classifications:
                    if self.is_null_classification(classification):
                        null_classifications += 1
                    else:
                        successful_classifications += 1
            else:
                failed_batches += 1
                # Count failed batch SKUs as null
                classifications = result.get('classifications', [])
                total_skus += len(classifications)
                null_classifications += len(classifications)
        
        return {
            'total_skus': total_skus,
            'successful_classifications': successful_classifications,
            'null_classifications': null_classifications,
            'failed_batches': failed_batches,
            'success_rate': successful_classifications / total_skus if total_skus > 0 else 0,
            'null_rate': null_classifications / total_skus if total_skus > 0 else 0,
            'model_used': 'gemini'
        }
