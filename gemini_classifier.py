"""
Gemini Classification Module for SKU Categorisation Pipeline
Handles Google Gemini API calls, prompt construction, and response parsing
"""
import json
import re
import logging
import time
from typing import List, Dict, Optional, Tuple
import google.generativeai as genai
from retry import retry
from config import Config

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class GeminiClassifier:
    """Handles Gemini-based SKU classification"""
    
    def __init__(self, config: Config = None):
        self.config = config or Config()
        
        # Configure Gemini
        genai.configure(api_key=self.config.GEMINI_API_KEY)
        
        # Initialize model
        self.model = genai.GenerativeModel(
            model_name=self.config.GEMINI_MODEL,
            generation_config=genai.types.GenerationConfig(
                temperature=0.1,  # Low temperature for consistent results
                max_output_tokens=2000,
                candidate_count=1
            )
        )
        
    @retry(tries=3, delay=1, backoff=2)
    def call_gemini_api(self, system_prompt: str, user_prompt: str) -> str:
        """
        Make API call to Gemini with retry logic
        
        Args:
            system_prompt: System prompt for Gemini
            user_prompt: User prompt with SKU data
            
        Returns:
            Gemini response text
        """
        try:
            # Combine system and user prompts for Gemini
            combined_prompt = f"{system_prompt}\n\n{user_prompt}"
            
            response = self.model.generate_content(combined_prompt)
            
            if response.candidates and response.candidates[0].content:
                return response.candidates[0].content.parts[0].text.strip()
            else:
                raise Exception("No valid response from Gemini")
            
        except Exception as e:
            logger.error(f"Gemini API call failed: {str(e)}")
            raise
    
    def clean_gemini_response(self, response: str) -> str:
        """
        Clean Gemini response to extract JSON
        
        Args:
            response: Raw Gemini response
            
        Returns:
            Cleaned JSON string
        """
        # Remove markdown code blocks if present
        response = re.sub(r'```json\s*', '', response)
        response = re.sub(r'```\s*', '', response)
        
        # Try to extract JSON array using regex
        json_pattern = r'\[.*\]'
        match = re.search(json_pattern, response, re.DOTALL)
        
        if match:
            return match.group(0)
        
        # If no array found, return original response
        return response.strip()
    
    def parse_gemini_response(self, response: str, expected_count: int) -> List[Dict]:
        """
        Parse Gemini response into structured classifications
        
        Args:
            response: Gemini response text
            expected_count: Expected number of classifications
            
        Returns:
            List of classification dictionaries
        """
        try:
            # Clean the response
            cleaned_response = self.clean_gemini_response(response)
            
            # Parse JSON
            classifications = json.loads(cleaned_response)
            
            # Validate structure
            if not isinstance(classifications, list):
                raise ValueError("Response is not a JSON array")
            
            # Ensure we have the expected number of results
            if len(classifications) != expected_count:
                logger.warning(f"Expected {expected_count} classifications, got {len(classifications)}")
            
            # Validate each classification
            validated_classifications = []
            for i, classification in enumerate(classifications):
                validated = self._validate_classification(classification, i)
                validated_classifications.append(validated)
            
            return validated_classifications
            
        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse JSON response: {str(e)}")
            logger.error(f"Response was: {response}")
            # Return null classifications for all expected items
            return [self._get_null_classification() for _ in range(expected_count)]
        
        except Exception as e:
            logger.error(f"Error parsing Gemini response: {str(e)}")
            return [self._get_null_classification() for _ in range(expected_count)]
    
    def _validate_classification(self, classification: Dict, index: int) -> Dict:
        """
        Validate and normalize a single classification
        
        Args:
            classification: Classification dictionary
            index: Index for error reporting
            
        Returns:
            Validated classification dictionary
        """
        required_fields = ['Tier0', 'Tier1', 'Tier2']
        
        # Ensure all required fields exist
        for field in required_fields:
            if field not in classification:
                logger.warning(f"Missing field '{field}' in classification {index}")
                classification[field] = None
        
        # Normalize null values
        for field in required_fields:
            value = classification[field]
            if value is None or str(value).strip().lower() in ['null', '', 'none']:
                classification[field] = None
        
        return classification
    
    def _get_null_classification(self) -> Dict:
        """Get a null classification dictionary"""
        return {
            'Tier0': None,
            'Tier1': None,
            'Tier2': None
        }
    
    def classify_batch(self, batch_data: List[Dict], batch_metadata: Dict) -> Dict:
        """
        Classify a batch of SKUs using Gemini
        
        Args:
            batch_data: List of SKU information dictionaries
            batch_metadata: Batch metadata
            
        Returns:
            Dictionary with classification results
        """
        batch_num = batch_metadata.get('batch_number', 0)
        logger.info(f"Classifying batch {batch_num} with {len(batch_data)} SKUs using Gemini")
        
        try:
            # Create user prompt
            user_prompt = self._create_batch_user_prompt(batch_data)
            
            # Call Gemini API
            response = self.call_gemini_api(self.config.SYSTEM_PROMPT, user_prompt)
            
            # Parse response
            classifications = self.parse_gemini_response(response, len(batch_data))
            
            # Create result
            result = {
                'batch_number': batch_num,
                'batch_metadata': batch_metadata,
                'classifications': classifications,
                'raw_response': response,
                'success': True,
                'timestamp': time.time(),
                'model_used': 'gemini'
            }
            
            logger.info(f"Successfully classified batch {batch_num} with Gemini")
            return result
            
        except Exception as e:
            logger.error(f"Error classifying batch {batch_num} with Gemini: {str(e)}")
            
            # Return error result with null classifications
            return {
                'batch_number': batch_num,
                'batch_metadata': batch_metadata,
                'classifications': [self._get_null_classification() for _ in batch_data],
                'error': str(e),
                'success': False,
                'timestamp': time.time(),
                'model_used': 'gemini'
            }
    
    def _create_batch_user_prompt(self, batch_data: List[Dict]) -> str:
        """
        Create user prompt for a batch of SKUs
        
        Args:
            batch_data: List of SKU information dictionaries
            
        Returns:
            Formatted user prompt
        """
        prompt_lines = ["SKUs:"]
        
        for sku in batch_data:
            line = (f"- Company: {sku['company_name']}, "
                   f"SKU: {sku['sku_name']}, "
                   f"Primary: {sku['primary_category']}, "
                   f"Secondary: {sku['secondary_category']}")
            prompt_lines.append(line)
        
        return "\n".join(prompt_lines)
    
    def is_null_classification(self, classification: Dict) -> bool:
        """
        Check if a classification is null/uncertain
        
        Args:
            classification: Classification dictionary
            
        Returns:
            True if classification is null/uncertain
        """
        required_fields = ['Tier0', 'Tier1', 'Tier2']
        
        return all(
            classification.get(field) is None or 
            str(classification.get(field)).strip().lower() in ['null', '', 'none']
            for field in required_fields
        )
    
    def get_classification_stats(self, batch_results: List[Dict]) -> Dict:
        """
        Get statistics about classification results
        
        Args:
            batch_results: List of batch results
            
        Returns:
            Statistics dictionary
        """
        total_skus = 0
        successful_classifications = 0
        null_classifications = 0
        failed_batches = 0
        
        for result in batch_results:
            if result.get('success', False):
                classifications = result.get('classifications', [])
                total_skus += len(classifications)
                
                for classification in classifications:
                    if self.is_null_classification(classification):
                        null_classifications += 1
                    else:
                        successful_classifications += 1
            else:
                failed_batches += 1
                # Count failed batch SKUs as null
                classifications = result.get('classifications', [])
                total_skus += len(classifications)
                null_classifications += len(classifications)
        
        return {
            'total_skus': total_skus,
            'successful_classifications': successful_classifications,
            'null_classifications': null_classifications,
            'failed_batches': failed_batches,
            'success_rate': successful_classifications / total_skus if total_skus > 0 else 0,
            'null_rate': null_classifications / total_skus if total_skus > 0 else 0,
            'model_used': 'gemini'
        }
