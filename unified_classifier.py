"""
Unified Classifier Interface for SKU Categorisation Pipeline
Provides a single interface for both OpenAI GPT and Google Gemini models
"""
import logging
from abc import ABC, abstractmethod
from typing import List, Dict, Optional
from config import Config
from gpt_classifier import GPTClassifier
from gemini_classifier import GeminiClassifier

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class BaseClassifier(ABC):
    """Abstract base class for all classifiers"""
    
    def __init__(self, config: Config = None):
        self.config = config or Config()
    
    @abstractmethod
    def classify_batch(self, batch_data: List[Dict], batch_metadata: Dict) -> Dict:
        """
        Classify a batch of SKUs
        
        Args:
            batch_data: List of SKU information dictionaries
            batch_metadata: Batch metadata
            
        Returns:
            Dictionary with classification results
        """
        pass
    
    @abstractmethod
    def is_null_classification(self, classification: Dict) -> bool:
        """
        Check if a classification is null/uncertain
        
        Args:
            classification: Classification dictionary
            
        Returns:
            True if classification is null/uncertain
        """
        pass
    
    @abstractmethod
    def get_classification_stats(self, batch_results: List[Dict]) -> Dict:
        """
        Get statistics about classification results
        
        Args:
            batch_results: List of batch results
            
        Returns:
            Statistics dictionary
        """
        pass

class UnifiedClassifier:
    """
    Unified classifier that can use either OpenAI GPT or Google Gemini
    based on configuration
    """
    
    def __init__(self, config: Config = None):
        self.config = config or Config()
        self.classifier = self._initialize_classifier()
        
        logger.info(f"Initialized classifier with provider: {self.config.MODEL_PROVIDER}")
    
    def _initialize_classifier(self) -> BaseClassifier:
        """Initialize the appropriate classifier based on configuration"""
        
        if self.config.MODEL_PROVIDER == 'openai':
            return GPTClassifier(self.config)
        elif self.config.MODEL_PROVIDER == 'gemini':
            return GeminiClassifier(self.config)
        else:
            raise ValueError(f"Unsupported model provider: {self.config.MODEL_PROVIDER}")
    
    def classify_batch(self, batch_data: List[Dict], batch_metadata: Dict) -> Dict:
        """
        Classify a batch of SKUs using the configured model
        
        Args:
            batch_data: List of SKU information dictionaries
            batch_metadata: Batch metadata
            
        Returns:
            Dictionary with classification results
        """
        return self.classifier.classify_batch(batch_data, batch_metadata)
    
    def is_null_classification(self, classification: Dict) -> bool:
        """
        Check if a classification is null/uncertain
        
        Args:
            classification: Classification dictionary
            
        Returns:
            True if classification is null/uncertain
        """
        return self.classifier.is_null_classification(classification)
    
    def get_classification_stats(self, batch_results: List[Dict]) -> Dict:
        """
        Get statistics about classification results
        
        Args:
            batch_results: List of batch results
            
        Returns:
            Statistics dictionary
        """
        stats = self.classifier.get_classification_stats(batch_results)
        stats['provider'] = self.config.MODEL_PROVIDER
        return stats
    
    def get_model_info(self) -> Dict:
        """
        Get information about the current model configuration
        
        Returns:
            Dictionary with model information
        """
        if self.config.MODEL_PROVIDER == 'openai':
            return {
                'provider': 'openai',
                'model': self.config.OPENAI_MODEL,
                'api_key_configured': bool(self.config.OPENAI_API_KEY)
            }
        elif self.config.MODEL_PROVIDER == 'gemini':
            return {
                'provider': 'gemini',
                'model': self.config.GEMINI_MODEL,
                'api_key_configured': bool(self.config.GEMINI_API_KEY)
            }
        else:
            return {
                'provider': 'unknown',
                'model': 'unknown',
                'api_key_configured': False
            }

    def call_api(self, system_prompt: str, user_prompt: str) -> str:
        """
        Make API call using the configured model

        Args:
            system_prompt: System prompt
            user_prompt: User prompt

        Returns:
            Model response text
        """
        if hasattr(self.classifier, 'call_gpt_api'):
            return self.classifier.call_gpt_api(system_prompt, user_prompt)
        elif hasattr(self.classifier, 'call_gemini_api'):
            return self.classifier.call_gemini_api(system_prompt, user_prompt)
        else:
            raise NotImplementedError(f"API call method not found for {self.config.MODEL_PROVIDER}")

    def parse_response(self, response: str, expected_count: int) -> List[Dict]:
        """
        Parse model response into structured classifications

        Args:
            response: Model response text
            expected_count: Expected number of classifications

        Returns:
            List of classification dictionaries
        """
        if hasattr(self.classifier, 'parse_gpt_response'):
            return self.classifier.parse_gpt_response(response, expected_count)
        elif hasattr(self.classifier, 'parse_gemini_response'):
            return self.classifier.parse_gemini_response(response, expected_count)
        else:
            raise NotImplementedError(f"Response parsing method not found for {self.config.MODEL_PROVIDER}")

    def switch_provider(self, provider: str) -> bool:
        """
        Switch to a different model provider
        
        Args:
            provider: New provider ('openai' or 'gemini')
            
        Returns:
            True if switch was successful
        """
        if provider not in ['openai', 'gemini']:
            logger.error(f"Invalid provider: {provider}")
            return False
        
        # Validate that the new provider has required configuration
        old_provider = self.config.MODEL_PROVIDER
        self.config.MODEL_PROVIDER = provider
        
        try:
            # Test initialization
            new_classifier = self._initialize_classifier()
            
            # If successful, update the classifier
            self.classifier = new_classifier
            logger.info(f"Successfully switched from {old_provider} to {provider}")
            return True
            
        except Exception as e:
            # Revert on failure
            self.config.MODEL_PROVIDER = old_provider
            logger.error(f"Failed to switch to {provider}: {str(e)}")
            return False

class ClassifierFactory:
    """Factory class for creating classifiers"""
    
    @staticmethod
    def create_classifier(provider: str = None, config: Config = None) -> UnifiedClassifier:
        """
        Create a classifier instance
        
        Args:
            provider: Model provider ('openai' or 'gemini')
            config: Configuration object
            
        Returns:
            UnifiedClassifier instance
        """
        if config is None:
            config = Config()
        
        if provider:
            config.MODEL_PROVIDER = provider
        
        return UnifiedClassifier(config)
    
    @staticmethod
    def get_available_providers() -> List[str]:
        """
        Get list of available model providers
        
        Returns:
            List of provider names
        """
        return ['openai', 'gemini']
    
    @staticmethod
    def validate_provider_config(provider: str, config: Config = None) -> Dict:
        """
        Validate configuration for a specific provider
        
        Args:
            provider: Provider to validate
            config: Configuration object
            
        Returns:
            Validation result dictionary
        """
        if config is None:
            config = Config()
        
        result = {
            'provider': provider,
            'valid': False,
            'errors': [],
            'warnings': []
        }
        
        if provider == 'openai':
            if not config.OPENAI_API_KEY:
                result['errors'].append('OPENAI_API_KEY is not configured')
            else:
                result['valid'] = True
                
        elif provider == 'gemini':
            if not config.GEMINI_API_KEY:
                result['errors'].append('GEMINI_API_KEY is not configured')
            else:
                result['valid'] = True
                
        else:
            result['errors'].append(f'Unknown provider: {provider}')
        
        return result

# Convenience functions for backward compatibility
def create_classifier(config: Config = None) -> UnifiedClassifier:
    """Create a classifier using the default configuration"""
    return UnifiedClassifier(config)

def get_classifier_for_provider(provider: str, config: Config = None) -> UnifiedClassifier:
    """Create a classifier for a specific provider"""
    return ClassifierFactory.create_classifier(provider, config)
