# SKU Categorisation Pipeline Configuration
# Copy this file to .env and configure your API keys

# Model Provider Selection
# Choose either 'openai' or 'gemini' - only one will be used at a time
# You can switch between them using: python model_switcher.py --switch <provider>
MODEL_PROVIDER=gemini

# OpenAI Configuration
# Get your API key from: https://platform.openai.com/api-keys
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_MODEL=gpt-4o-mini

# Gemini Configuration
# Get your API key from: https://makersuite.google.com/app/apikey
GEMINI_API_KEY=AIzaSyArGEWvUp4il7FUUuWuK9bzUw513FFP18c
GEMINI_MODEL=gemini-1.5-flash

# Note: You only need to configure the API key for the provider you want to use
# However, having both configured allows you to easily switch between them

# Optional: Override default settings
# BATCH_SIZE=20
# MAX_RETRIES=3
# ENABLE_WEB_FALLBACK=True
# ENABLE_CACHING=True
