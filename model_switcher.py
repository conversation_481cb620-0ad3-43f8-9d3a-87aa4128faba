"""
Model Switcher Utility for SKU Categorisation Pipeline
Provides easy switching between ChatGPT and Gemini classifiers
"""
import os
import sys
import argparse
from pathlib import Path
from config import Config
from unified_classifier import UnifiedClassifier

def check_api_keys():
    """Check which API keys are available"""
    config = Config()
    
    openai_available = bool(config.OPENAI_API_KEY)
    gemini_available = bool(config.GEMINI_API_KEY)
    
    print("API Key Status:")
    print(f"  OpenAI: {'✓ Available' if openai_available else '❌ Not configured'}")
    print(f"  Gemini: {'✓ Available' if gemini_available else '❌ Not configured'}")
    
    return openai_available, gemini_available

def get_current_provider():
    """Get the currently configured provider"""
    config = Config()
    return config.get_current_provider()

def switch_provider(new_provider: str):
    """
    Switch to a different model provider
    
    Args:
        new_provider: 'openai' or 'gemini'
    """
    config = Config()
    
    # Check if the provider is available
    if not config.is_provider_available(new_provider):
        print(f"❌ Cannot switch to {new_provider}: API key not configured")
        return False
    
    # Update environment variable for this session
    os.environ['MODEL_PROVIDER'] = new_provider
    
    # Test the classifier initialization
    try:
        test_config = Config()
        test_config.set_model_provider(new_provider)
        classifier = UnifiedClassifier(test_config)
        model_info = classifier.get_model_info()
        
        print(f"✓ Successfully switched to {new_provider}")
        print(f"  Model: {model_info['model']}")
        print(f"  Provider: {model_info['provider']}")
        
        return True
        
    except Exception as e:
        print(f"❌ Failed to switch to {new_provider}: {str(e)}")
        return False

def create_env_file(openai_key: str = None, gemini_key: str = None, default_provider: str = 'openai'):
    """
    Create or update .env file with API keys
    
    Args:
        openai_key: OpenAI API key
        gemini_key: Gemini API key  
        default_provider: Default model provider
    """
    env_content = []
    
    # Add model provider
    env_content.append(f"MODEL_PROVIDER={default_provider}")
    env_content.append("")
    
    # Add OpenAI configuration
    env_content.append("# OpenAI Configuration")
    if openai_key:
        env_content.append(f"OPENAI_API_KEY={openai_key}")
    else:
        env_content.append("# OPENAI_API_KEY=your_openai_api_key_here")
    env_content.append("OPENAI_MODEL=gpt-4o-mini")
    env_content.append("")
    
    # Add Gemini configuration
    env_content.append("# Gemini Configuration")
    if gemini_key:
        env_content.append(f"GEMINI_API_KEY={gemini_key}")
    else:
        env_content.append("# GEMINI_API_KEY=your_gemini_api_key_here")
    env_content.append("GEMINI_MODEL=gemini-1.5-flash")
    
    # Write to .env file
    with open('.env', 'w') as f:
        f.write('\n'.join(env_content))
    
    print("✓ Created .env file")
    print("  Please add your API keys to the .env file")

def show_status():
    """Show current model configuration status"""
    print("Current Model Configuration:")
    print("=" * 40)
    
    current_provider = get_current_provider()
    print(f"Active Provider: {current_provider.upper()}")
    
    openai_available, gemini_available = check_api_keys()
    
    print(f"\nAvailable Providers:")
    if openai_available:
        print(f"  • openai {'(ACTIVE)' if current_provider == 'openai' else ''}")
    if gemini_available:
        print(f"  • gemini {'(ACTIVE)' if current_provider == 'gemini' else ''}")
    
    if not openai_available and not gemini_available:
        print("  ❌ No providers available - please configure API keys")

def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(description='Model Switcher for SKU Categorisation Pipeline')
    parser.add_argument('--status', action='store_true', help='Show current model status')
    parser.add_argument('--switch', choices=['openai', 'gemini'], help='Switch to specified provider')
    parser.add_argument('--setup', action='store_true', help='Setup .env file with API keys')
    parser.add_argument('--openai-key', help='OpenAI API key for setup')
    parser.add_argument('--gemini-key', help='Gemini API key for setup')
    parser.add_argument('--default-provider', choices=['openai', 'gemini'], default='openai', 
                       help='Default provider for setup')
    
    args = parser.parse_args()
    
    if args.setup:
        create_env_file(args.openai_key, args.gemini_key, args.default_provider)
        return
    
    if args.switch:
        success = switch_provider(args.switch)
        if success:
            print(f"\n💡 To make this change permanent, update your .env file:")
            print(f"   MODEL_PROVIDER={args.switch}")
        return
    
    if args.status or len(sys.argv) == 1:
        show_status()
        return

if __name__ == "__main__":
    main()
