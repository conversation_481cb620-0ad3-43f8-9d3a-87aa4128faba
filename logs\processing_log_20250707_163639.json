{"timestamp": "2025-07-07T16:36:39.222925", "batch_results": [{"batch_number": 1, "batch_metadata": {"batch_number": 1, "batch_size": 5, "start_index": 0, "end_index": 4, "original_indices": [0, 1, 2, 3, 4]}, "classifications": [{"Tier0": "Personal Care", "Tier1": "Hair Care", "Tier2": "Hair Oil"}, {"Tier0": "Food & Beverages", "Tier1": "Dairy", "Tier2": "Condensed Milk"}, {"Tier0": "Health & Wellness", "Tier1": "Ayurvedic", "Tier2": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"Tier0": "Personal Care", "Tier1": "Skin Care", "Tier2": "Rose Water"}, {"Tier0": "Food & Beverages", "Tier1": "Biscuits", "Tier2": "Cookies"}], "raw_response": "```json\n[\n  {\n    \"Tier0\": \"Personal Care\",\n    \"Tier1\": \"Hair Care\",\n    \"Tier2\": \"Hair Oil\"\n  },\n  {\n    \"Tier0\": \"Food & Beverages\",\n    \"Tier1\": \"Dairy\",\n    \"Tier2\": \"Condensed Milk\"\n  },\n  {\n    \"Tier0\": \"Health & Wellness\",\n    \"Tier1\": \"Ayurvedic\",\n    \"Tier2\": \"<PERSON><PERSON><PERSON><PERSON><PERSON>\"\n  },\n  {\n    \"Tier0\": \"Personal Care\",\n    \"Tier1\": \"Skin Care\",\n    \"Tier2\": \"Rose Water\"\n  },\n  {\n    \"Tier0\": \"Food & Beverages\",\n    \"Tier1\": \"Biscuits\",\n    \"Tier2\": \"Cookies\"\n  }\n]\n```", "success": true, "timestamp": **********.3595233, "model_used": "gemini"}, {"batch_number": 2, "batch_metadata": {"batch_number": 2, "batch_size": 5, "start_index": 5, "end_index": 9, "original_indices": [5, 6, 7, 8, 9]}, "classifications": [{"Tier0": "Food & Beverages", "Tier1": "Dairy", "Tier2": "Condensed Milk"}, {"Tier0": "Food & Beverages", "Tier1": "Coffee", "Tier2": "Instant Coffee"}, {"Tier0": "Health & Wellness", "Tier1": "Ayurvedic", "Tier2": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"Tier0": "Home Care", "Tier1": "Detergent", "Tier2": "<PERSON><PERSON><PERSON> Deter<PERSON>"}, {"Tier0": "Food & Beverages", "Tier1": "<PERSON><PERSON>", "Tier2": "Bread"}], "raw_response": "```json\n[\n  {\n    \"Tier0\": \"Food & Beverages\",\n    \"Tier1\": \"Dairy\",\n    \"Tier2\": \"Condensed Milk\"\n  },\n  {\n    \"Tier0\": \"Food & Beverages\",\n    \"Tier1\": \"Coffee\",\n    \"Tier2\": \"Instant Coffee\"\n  },\n  {\n    \"Tier0\": \"Health & Wellness\",\n    \"Tier1\": \"Ayurvedic\",\n    \"Tier2\": \"<PERSON><PERSON><PERSON><PERSON><PERSON>\"\n  },\n  {\n    \"Tier0\": \"Home Care\",\n    \"Tier1\": \"Detergent\",\n    \"Tier2\": \"Laundry Detergent\"\n  },\n  {\n    \"Tier0\": \"Food & Beverages\",\n    \"Tier1\": \"Bakery\",\n    \"Tier2\": \"Bread\"\n  }\n]\n```", "success": true, "timestamp": **********.3412914, "model_used": "gemini"}, {"batch_number": 3, "batch_metadata": {"batch_number": 3, "batch_size": 5, "start_index": 10, "end_index": 14, "original_indices": [10, 11, 12, 13, 14]}, "classifications": [{"Tier0": "Food & Beverages", "Tier1": "Honey", "Tier2": "Honey"}, {"Tier0": "Personal Care", "Tier1": "Bath & Body", "Tier2": "Bar Soap"}, {"Tier0": "Personal Care", "Tier1": "Oral Care", "Tier2": "Toothpaste"}, {"Tier0": "Food & Beverages", "Tier1": "Instant Food", "Tier2": "Noodles"}, {"Tier0": "Food & Beverages", "Tier1": "Confectionery", "Tier2": "Chocolate"}], "raw_response": "```json\n[\n  {\n    \"Tier0\": \"Food & Beverages\",\n    \"Tier1\": \"Honey\",\n    \"Tier2\": \"Honey\"\n  },\n  {\n    \"Tier0\": \"Personal Care\",\n    \"Tier1\": \"Bath & Body\",\n    \"Tier2\": \"Bar Soap\"\n  },\n  {\n    \"Tier0\": \"Personal Care\",\n    \"Tier1\": \"Oral Care\",\n    \"Tier2\": \"Toothpaste\"\n  },\n  {\n    \"Tier0\": \"Food & Beverages\",\n    \"Tier1\": \"Instant Food\",\n    \"Tier2\": \"Noodles\"\n  },\n  {\n    \"Tier0\": \"Food & Beverages\",\n    \"Tier1\": \"Confectionery\",\n    \"Tier2\": \"Chocolate\"\n  }\n]\n```", "success": true, "timestamp": 1751886395.469721, "model_used": "gemini"}, {"batch_number": 4, "batch_metadata": {"batch_number": 4, "batch_size": 5, "start_index": 15, "end_index": 19, "original_indices": [15, 16, 17, 18, 19]}, "classifications": [{"Tier0": "Personal Care", "Tier1": "Hair Care", "Tier2": "Hair Oil"}, {"Tier0": "Food & Beverages", "Tier1": "Confectionery", "Tier2": "Chocolate"}, {"Tier0": "Personal Care", "Tier1": "Skin Care", "Tier2": "Rose Water"}, {"Tier0": "Baby Care", "Tier1": "Baby Food", "Tier2": "Baby Food"}, {"Tier0": "Home Care", "Tier1": "Detergent", "Tier2": "<PERSON><PERSON><PERSON> Deter<PERSON>"}], "raw_response": "```json\n[\n  {\n    \"Tier0\": \"Personal Care\",\n    \"Tier1\": \"Hair Care\",\n    \"Tier2\": \"Hair Oil\"\n  },\n  {\n    \"Tier0\": \"Food & Beverages\",\n    \"Tier1\": \"Confectionery\",\n    \"Tier2\": \"Chocolate\"\n  },\n  {\n    \"Tier0\": \"Personal Care\",\n    \"Tier1\": \"Skin Care\",\n    \"Tier2\": \"Rose Water\"\n  },\n  {\n    \"Tier0\": \"Baby Care\",\n    \"Tier1\": \"Baby Food\",\n    \"Tier2\": \"Baby Food\"\n  },\n  {\n    \"Tier0\": \"Home Care\",\n    \"Tier1\": \"Detergent\",\n    \"Tier2\": \"Laundry Detergent\"\n  }\n]\n```", "success": true, "timestamp": 1751886397.2752612, "model_used": "gemini"}, {"batch_number": 5, "batch_metadata": {"batch_number": 5, "batch_size": 5, "start_index": 20, "end_index": 24, "original_indices": [20, 21, 22, 23, 24]}, "classifications": [{"Tier0": "Home Care", "Tier1": "<PERSON><PERSON><PERSON>", "Tier2": "<PERSON><PERSON><PERSON> Gel"}, {"Tier0": "Personal Care", "Tier1": "Oral Care", "Tier2": "Toothpaste"}, {"Tier0": "Personal Care", "Tier1": "Hair Care", "Tier2": "Shampoo"}, {"Tier0": "Baby Care", "Tier1": "Baby Food", "Tier2": "<PERSON>"}, {"Tier0": "Food & Beverages", "Tier1": "Tea", "Tier2": "Green Tea"}], "raw_response": "[\n  {\n    \"Tier0\": \"Home Care\",\n    \"Tier1\": \"Dishwash\",\n    \"Tier2\": \"Dishwash Gel\"\n  },\n  {\n    \"Tier0\": \"Personal Care\",\n    \"Tier1\": \"Oral Care\",\n    \"Tier2\": \"Toothpaste\"\n  },\n  {\n    \"Tier0\": \"Personal Care\",\n    \"Tier1\": \"Hair Care\",\n    \"Tier2\": \"Shampoo\"\n  },\n  {\n    \"Tier0\": \"Baby Care\",\n    \"Tier1\": \"Baby Food\",\n    \"Tier2\": \"Baby Cereal\"\n  },\n  {\n    \"Tier0\": \"Food & Beverages\",\n    \"Tier1\": \"Tea\",\n    \"Tier2\": \"Green Tea\"\n  }\n]", "success": true, "timestamp": 1751886399.154218, "model_used": "gemini"}], "fallback_results": [], "summary_stats": {"total_skus": 25, "complete_classifications": "25", "partial_classifications": "0", "needs_manual_review": "0", "completion_rate": 1.0, "manual_review_rate": 0.0, "source_breakdown": {"GPT Primary": 25}, "tier_fill_rates": {"tier0": 1.0, "tier1": 1.0, "tier2": 1.0}}, "config": {"batch_size": 5, "model": "gpt-4o-mini", "enable_web_fallback": true}}