"""
Example Usage Script for SKU Categorisation Pipeline
Demonstrates how to use both OpenAI and Gemini models
"""
import os
import sys
from pathlib import Path

# Add current directory to path for imports
sys.path.append(str(Path(__file__).parent))

from config import Config
from unified_classifier import UnifiedClassifier, ClassifierFactory
from generate_sample_data import generate_sample_sku_data

def demonstrate_model_switching():
    """Demonstrate switching between OpenAI and Gemini models"""
    
    print("SKU Categorisation Pipeline - Model Demonstration")
    print("=" * 50)
    
    # Generate sample data
    print("Generating sample SKU data...")
    sample_df = generate_sample_sku_data(5)
    print(f"Generated {len(sample_df)} sample SKUs")
    print()
    
    # Prepare sample batch data
    batch_data = []
    for _, row in sample_df.iterrows():
        batch_data.append({
            'sku_name': row['SKUName'],
            'company_name': row['CompanyName'],
            'primary_category': row['PrimaryCategoryName'],
            'secondary_category': row['SecondaryCategoryName']
        })
    
    batch_metadata = {
        'batch_number': 1,
        'original_indices': list(range(len(batch_data)))
    }
    
    # Test available providers
    print("Available Model Providers:")
    providers = ClassifierFactory.get_available_providers()
    for provider in providers:
        print(f"- {provider}")
    print()
    
    # Test each provider
    for provider in providers:
        print(f"Testing {provider.upper()} Provider:")
        print("-" * 30)
        
        # Validate configuration
        config = Config()
        validation = ClassifierFactory.validate_provider_config(provider, config)
        
        if validation['valid']:
            print(f"✓ {provider} configuration is valid")
            
            try:
                # Create classifier
                classifier = ClassifierFactory.create_classifier(provider, config)
                model_info = classifier.get_model_info()
                
                print(f"✓ Model: {model_info['model']}")
                print(f"✓ API Key Configured: {model_info['api_key_configured']}")
                
                # Note: We won't actually call the API in this demo
                # to avoid using API credits
                print("✓ Classifier initialized successfully")
                print("  (Skipping actual API call to preserve credits)")
                
            except Exception as e:
                print(f"❌ Failed to initialize {provider}: {str(e)}")
                
        else:
            print(f"❌ {provider} configuration invalid:")
            for error in validation['errors']:
                print(f"   - {error}")
        
        print()
    
    # Demonstrate unified interface
    print("Unified Classifier Interface:")
    print("-" * 30)
    
    try:
        # Create default classifier
        classifier = UnifiedClassifier()
        model_info = classifier.get_model_info()
        
        print(f"✓ Default provider: {model_info['provider']}")
        print(f"✓ Model: {model_info['model']}")
        
        # Try switching providers (if both are configured)
        config = Config()
        if config.OPENAI_API_KEY and config.GEMINI_API_KEY:
            print("✓ Both providers configured - testing switch...")
            
            current_provider = classifier.get_model_info()['provider']
            new_provider = 'gemini' if current_provider == 'openai' else 'openai'
            
            if classifier.switch_provider(new_provider):
                print(f"✓ Successfully switched to {new_provider}")
                print(f"✓ New model: {classifier.get_model_info()['model']}")
            else:
                print(f"❌ Failed to switch to {new_provider}")
        else:
            print("⚠️  Only one provider configured - cannot test switching")
            
    except Exception as e:
        print(f"❌ Unified classifier error: {str(e)}")
    
    print()
    print("Demo completed!")
    print()
    print("To run the full pipeline:")
    print("1. Set your API keys in .env file")
    print("2. Run: python main.py --input data/SKU_Categorisation_Master.xlsx")
    print("3. Or with specific model: python main.py --model gemini --input data/SKU_Categorisation_Master.xlsx")

def show_configuration_help():
    """Show configuration help"""
    
    print("Configuration Help")
    print("=" * 20)
    print()
    
    print("Environment Variables:")
    print("- MODEL_PROVIDER: 'openai' or 'gemini' (default: 'openai')")
    print("- OPENAI_API_KEY: Your OpenAI API key")
    print("- OPENAI_MODEL: OpenAI model name (default: 'gpt-4o-mini')")
    print("- GEMINI_API_KEY: Your Google Gemini API key")
    print("- GEMINI_MODEL: Gemini model name (default: 'gemini-1.5-flash')")
    print()
    
    print("Getting API Keys:")
    print("- OpenAI: https://platform.openai.com/api-keys")
    print("- Gemini: https://makersuite.google.com/app/apikey")
    print()
    
    print("Example .env file:")
    print("MODEL_PROVIDER=openai")
    print("OPENAI_API_KEY=your_openai_key_here")
    print("GEMINI_API_KEY=your_gemini_key_here")

def main():
    """Main function"""
    
    if len(sys.argv) > 1 and sys.argv[1] == '--help-config':
        show_configuration_help()
    else:
        demonstrate_model_switching()

if __name__ == "__main__":
    main()
