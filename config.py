"""
Configuration settings for SKU Categorisation Pipeline
"""
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

class Config:
    # Model Selection
    MODEL_PROVIDER = os.getenv('MODEL_PROVIDER', 'openai')  # 'openai' or 'gemini'

    # OpenAI Configuration
    OPENAI_API_KEY = os.getenv('OPENAI_API_KEY')
    OPENAI_MODEL = os.getenv('OPENAI_MODEL', 'gpt-4o-mini')

    # Gemini Configuration
    GEMINI_API_KEY = os.getenv('GEMINI_API_KEY')
    GEMINI_MODEL = os.getenv('GEMINI_MODEL', 'gemini-1.5-flash')

    def set_model_provider(self, provider: str):
        """
        Set the model provider dynamically

        Args:
            provider: 'openai' or 'gemini'
        """
        if provider not in ['openai', 'gemini']:
            raise ValueError("Provider must be 'openai' or 'gemini'")
        self.MODEL_PROVIDER = provider

    def get_current_provider(self) -> str:
        """Get the currently configured model provider"""
        return self.MODEL_PROVIDER

    def is_provider_available(self, provider: str) -> bool:
        """
        Check if a provider is available (has API key configured)

        Args:
            provider: 'openai' or 'gemini'

        Returns:
            True if provider is available, False otherwise
        """
        if provider == 'openai':
            return bool(self.OPENAI_API_KEY)
        elif provider == 'gemini':
            return bool(self.GEMINI_API_KEY)
        else:
            return False
    
    # Batch Processing Configuration
    BATCH_SIZE = 20
    MAX_RETRIES = 3
    RETRY_DELAY = 1  # seconds
    
    # File Configuration
    INPUT_FILE_PATH = 'data/SKU_Categorisation_Master.csv'
    OUTPUT_FILE_PATH = 'data/SKU_Categorisation_Enriched.csv'
    
    # Required columns in input file
    REQUIRED_COLUMNS = [
        'SKUId',
        'SKUName', 
        'PrimaryCategoryName',
        'SecondaryCategoryName',
        'CompanyId',
        'CompanyName'
    ]
    
    # Web Search Configuration
    ENABLE_WEB_FALLBACK = True
    SEARCH_SITES = ['bigbasket.com', 'amazon.in', 'flipkart.com']
    MAX_SEARCH_RESULTS = 3

    # Web Search Rate Limiting
    SEARCH_DELAY_SECONDS = 2.0  # Delay between search requests
    SEARCH_REQUESTS_PER_MINUTE = 20  # Maximum requests per minute
    SEARCH_BURST_LIMIT = 5  # Maximum burst requests before enforcing rate limit
    
    # Output Configuration
    OUTPUT_COLUMNS = [
        'Tier0',
        'Tier1', 
        'Tier2',
        'Source',
        'NeedsManualReview'
    ]
    
    # GPT System Prompt AND GEMINI PROMPT
    SYSTEM_PROMPT = """You are an expert FMCG/FMCD SKU classifier.

For each SKU, classify into a three-tier hierarchy:
- Tier0: Broad segment (e.g., Beverages, Personal Care)
- Tier1: Category (e.g., Tea, Shampoo)
- Tier2: Subcategory (e.g., Green Tea, Anti-Dandruff Shampoo)

If you are not confident or cannot infer the classification, return:
{
  "Tier0": null,
  "Tier1": null,
  "Tier2": null
}

Only return a raw JSON array. Do not guess. Do not wrap output in markdown or any text."""

    # Validation settings
    ENABLE_CACHING = True
    CACHE_FILE = 'cache/sku_classifications.json'
    DEFAULT_OUTPUT_FILE = 'data/SKU_Categorisation_Enriched.xlsx'
    
    @classmethod
    def validate(cls):
        """Validate configuration settings"""
        # Validate model provider
        if cls.MODEL_PROVIDER not in ['openai', 'gemini']:
            raise ValueError("MODEL_PROVIDER must be 'openai' or 'gemini'")

        # Validate API keys based on selected provider
        if cls.MODEL_PROVIDER == 'openai' and not cls.OPENAI_API_KEY:
            raise ValueError("OPENAI_API_KEY is required when using OpenAI models")

        if cls.MODEL_PROVIDER == 'gemini' and not cls.GEMINI_API_KEY:
            raise ValueError("GEMINI_API_KEY is required when using Gemini models")

        if cls.BATCH_SIZE <= 0:
            raise ValueError("BATCH_SIZE must be positive")

        # Create necessary directories
        import os
        if not os.path.exists(os.path.dirname(cls.CACHE_FILE)):
            os.makedirs(os.path.dirname(cls.CACHE_FILE), exist_ok=True)

        return True
