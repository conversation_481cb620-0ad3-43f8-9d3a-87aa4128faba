"""
Output Processing Module for SKU Categorisation Pipeline
Handles result consolidation and final file generation
"""
import pandas as pd
import logging
import json
from datetime import datetime
from typing import List, Dict, Optional, Tuple
from pathlib import Path
from config import Config

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class OutputProcessor:
    """Handles output processing and file generation for SKU categorisation results"""
    
    def __init__(self, config: Config = None):
        self.config = config or Config()
        
    def consolidate_results(self, 
                          original_df: pd.DataFrame,
                          batch_results: List[Dict],
                          fallback_results: List[Dict] = None) -> pd.DataFrame:
        """
        Consolidate all classification results into final DataFrame
        
        Args:
            original_df: Original input DataFrame
            batch_results: Results from GPT batch processing
            fallback_results: Results from web search fallback
            
        Returns:
            Consolidated DataFrame with all results
        """
        logger.info("Consolidating classification results")
        
        # Start with original DataFrame
        result_df = original_df.copy()
        
        # Initialize output columns
        for col in self.config.OUTPUT_COLUMNS:
            result_df[col] = None
        
        # Process batch results
        self._apply_batch_results(result_df, batch_results)
        
        # Process fallback results if available
        if fallback_results:
            self._apply_fallback_results(result_df, fallback_results)
        
        # Set final flags
        self._set_final_flags(result_df)
        
        logger.info(f"Consolidated results for {len(result_df)} SKUs")
        return result_df
    
    def _apply_batch_results(self, result_df: pd.DataFrame, batch_results: List[Dict]) -> None:
        """Apply batch processing results to DataFrame"""
        
        for batch_result in batch_results:
            if not batch_result.get('success', False):
                continue
                
            classifications = batch_result.get('classifications', [])
            batch_metadata = batch_result.get('batch_metadata', {})
            original_indices = batch_metadata.get('original_indices', [])
            
            for i, classification in enumerate(classifications):
                if i < len(original_indices):
                    original_idx = original_indices[i]
                    
                    if original_idx in result_df.index:
                        # Apply classification
                        result_df.loc[original_idx, 'Tier0_Segment'] = classification.get('Tier0')
                        result_df.loc[original_idx, 'Tier1_Category'] = classification.get('Tier1')
                        result_df.loc[original_idx, 'Tier2_SubCategory'] = classification.get('Tier2')
                        result_df.loc[original_idx, 'Source'] = 'GPT Primary'
    
    def _apply_fallback_results(self, result_df: pd.DataFrame, fallback_results: List[Dict]) -> None:
        """Apply fallback processing results to DataFrame"""
        
        for fallback_result in fallback_results:
            original_idx = fallback_result.get('original_index')
            
            if original_idx is not None and original_idx in result_df.index:
                classification = fallback_result.get('classification', {})
                
                # Only update if fallback was successful and provided better results
                if fallback_result.get('success', False):
                    result_df.loc[original_idx, 'Tier0_Segment'] = classification.get('Tier0')
                    result_df.loc[original_idx, 'Tier1_Category'] = classification.get('Tier1')
                    result_df.loc[original_idx, 'Tier2_SubCategory'] = classification.get('Tier2')
                    result_df.loc[original_idx, 'Source'] = 'Web Fallback'
    
    def _set_final_flags(self, result_df: pd.DataFrame) -> None:
        """Set final processing flags"""
        
        # Mark items that need manual review
        result_df['NeedsManualReview'] = (
            result_df['Tier0_Segment'].isna() & 
            result_df['Tier1_Category'].isna() & 
            result_df['Tier2_SubCategory'].isna()
        )
        
        # Set default source for items without classification
        result_df['Source'] = result_df['Source'].fillna('Unclassified')
    
    def generate_summary_stats(self, result_df: pd.DataFrame) -> Dict:
        """
        Generate summary statistics for the results
        
        Args:
            result_df: Final results DataFrame
            
        Returns:
            Dictionary with summary statistics
        """
        total_skus = len(result_df)
        
        # Count by source
        source_counts = result_df['Source'].value_counts().to_dict()
        
        # Count classifications by tier
        tier0_filled = result_df['Tier0_Segment'].notna().sum()
        tier1_filled = result_df['Tier1_Category'].notna().sum()
        tier2_filled = result_df['Tier2_SubCategory'].notna().sum()
        
        # Count complete classifications (all tiers filled)
        complete_classifications = (
            result_df['Tier0_Segment'].notna() & 
            result_df['Tier1_Category'].notna() & 
            result_df['Tier2_SubCategory'].notna()
        ).sum()
        
        # Count items needing manual review
        needs_manual_review = result_df['NeedsManualReview'].sum()
        
        summary = {
            'total_skus': total_skus,
            'complete_classifications': complete_classifications,
            'partial_classifications': total_skus - complete_classifications - needs_manual_review,
            'needs_manual_review': needs_manual_review,
            'completion_rate': complete_classifications / total_skus if total_skus > 0 else 0,
            'manual_review_rate': needs_manual_review / total_skus if total_skus > 0 else 0,
            'source_breakdown': source_counts,
            'tier_fill_rates': {
                'tier0': tier0_filled / total_skus if total_skus > 0 else 0,
                'tier1': tier1_filled / total_skus if total_skus > 0 else 0,
                'tier2': tier2_filled / total_skus if total_skus > 0 else 0
            }
        }
        
        return summary
    
    def save_results(self, result_df: pd.DataFrame, output_path: str = None) -> str:
        """
        Save results to CSV files

        Args:
            result_df: Final results DataFrame
            output_path: Output file path (main results CSV)

        Returns:
            Path to saved main results file
        """
        output_path = output_path or self.config.OUTPUT_FILE_PATH

        # Ensure output directory exists
        Path(output_path).parent.mkdir(parents=True, exist_ok=True)

        # Save main results to CSV
        result_df.to_csv(output_path, index=False, encoding='utf-8')
        logger.info(f"Main results saved to {output_path}")

        # Generate and save summary statistics
        summary_stats = self.generate_summary_stats(result_df)
        summary_df = self._create_summary_dataframe(summary_stats)

        # Create summary file path
        base_path = Path(output_path)
        summary_path = base_path.parent / f"{base_path.stem}_Summary{base_path.suffix}"
        summary_df.to_csv(summary_path, index=False, encoding='utf-8')
        logger.info(f"Summary statistics saved to {summary_path}")

        # Save manual review items if any exist
        manual_review_df = result_df[result_df['NeedsManualReview'] == True].copy()
        if not manual_review_df.empty:
            manual_review_path = base_path.parent / f"{base_path.stem}_ManualReview{base_path.suffix}"
            manual_review_df.to_csv(manual_review_path, index=False, encoding='utf-8')
            logger.info(f"Manual review items saved to {manual_review_path}")

        return output_path
    
    def _create_summary_dataframe(self, summary_stats: Dict) -> pd.DataFrame:
        """Create a DataFrame from summary statistics"""
        
        summary_data = []
        
        # Overall statistics
        summary_data.extend([
            {'Metric': 'Total SKUs', 'Value': summary_stats['total_skus']},
            {'Metric': 'Complete Classifications', 'Value': summary_stats['complete_classifications']},
            {'Metric': 'Partial Classifications', 'Value': summary_stats['partial_classifications']},
            {'Metric': 'Needs Manual Review', 'Value': summary_stats['needs_manual_review']},
            {'Metric': 'Completion Rate', 'Value': f"{summary_stats['completion_rate']:.2%}"},
            {'Metric': 'Manual Review Rate', 'Value': f"{summary_stats['manual_review_rate']:.2%}"},
            {'Metric': '', 'Value': ''},  # Separator
        ])
        
        # Source breakdown
        summary_data.append({'Metric': 'Source Breakdown', 'Value': ''})
        for source, count in summary_stats['source_breakdown'].items():
            summary_data.append({'Metric': f'  {source}', 'Value': count})
        
        summary_data.append({'Metric': '', 'Value': ''})  # Separator
        
        # Tier fill rates
        summary_data.append({'Metric': 'Tier Fill Rates', 'Value': ''})
        for tier, rate in summary_stats['tier_fill_rates'].items():
            summary_data.append({'Metric': f'  {tier.upper()}', 'Value': f"{rate:.2%}"})
        
        return pd.DataFrame(summary_data)
    
    def save_processing_log(self, 
                          batch_results: List[Dict],
                          fallback_results: List[Dict] = None,
                          summary_stats: Dict = None,
                          log_path: str = None) -> str:
        """
        Save detailed processing log
        
        Args:
            batch_results: Batch processing results
            fallback_results: Fallback processing results
            summary_stats: Summary statistics
            log_path: Log file path
            
        Returns:
            Path to saved log file
        """
        if log_path is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            log_path = f"logs/processing_log_{timestamp}.json"
        
        # Ensure log directory exists
        Path(log_path).parent.mkdir(parents=True, exist_ok=True)
        
        log_data = {
            'timestamp': datetime.now().isoformat(),
            'batch_results': batch_results,
            'fallback_results': fallback_results or [],
            'summary_stats': summary_stats or {},
            'config': {
                'batch_size': self.config.BATCH_SIZE,
                'model': self.config.OPENAI_MODEL,
                'enable_web_fallback': self.config.ENABLE_WEB_FALLBACK
            }
        }
        
        with open(log_path, 'w', encoding='utf-8') as f:
            json.dump(log_data, f, indent=2, ensure_ascii=False, default=str)
        
        logger.info(f"Processing log saved to {log_path}")
        return log_path
    
    def create_audit_report(self, result_df: pd.DataFrame) -> pd.DataFrame:
        """
        Create audit report with detailed classification information
        
        Args:
            result_df: Final results DataFrame
            
        Returns:
            Audit report DataFrame
        """
        audit_data = []
        
        for _, row in result_df.iterrows():
            audit_record = {
                'SKU_ID': row.get('SKUId'),
                'SKU_Name': row.get('SKUName'),
                'Company': row.get('CompanyName'),
                'Original_Primary': row.get('PrimaryCategoryName'),
                'Original_Secondary': row.get('SecondaryCategoryName'),
                'Classified_Tier0': row.get('Tier0_Segment'),
                'Classified_Tier1': row.get('Tier1_Category'),
                'Classified_Tier2': row.get('Tier2_SubCategory'),
                'Classification_Source': row.get('Source'),
                'Needs_Manual_Review': row.get('NeedsManualReview'),
                'Classification_Complete': not row.get('NeedsManualReview', True)
            }
            audit_data.append(audit_record)
        
        return pd.DataFrame(audit_data)
